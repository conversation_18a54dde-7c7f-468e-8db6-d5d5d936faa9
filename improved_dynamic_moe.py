import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import math
from typing import Optional, Tuple, List
import warnings

# 定义专家网络（FFN）
class FFN(nn.Module):
    def __init__(self, input_dim=768, hidden_dim=1024, dropout=0.1):
        super(FFN, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, input_dim)
        self.act = nn.GELU()
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.dropout(x)
        x = self.fc2(x)
        return x

# 改进的动态MoE层
class ImprovedDynamicMoE(nn.Module):
    def __init__(self, num_experts=64, top_k=4, hidden_dim=768, capacity_factor=1.25):
        super(ImprovedDynamicMoE, self).__init__()
        self.num_experts = num_experts
        self.top_k = top_k
        self.hidden_dim = hidden_dim
        self.capacity_factor = capacity_factor
        
        # 专家网络
        self.experts = nn.ModuleList([FFN(hidden_dim) for _ in range(num_experts)])
        
        # 改进的门控网络
        self.gating = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, num_experts)
        )
        
        # 动态top_k预测器
        self.k_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 负载均衡统计
        self.register_buffer('expert_counts', torch.zeros(num_experts))
        self.register_buffer('total_tokens', torch.tensor(0.0))

    def forward(self, x, complexity_score=None):
        batch_size, seq_len, hidden_dim = x.shape
        
        # 计算复杂度分数（如果没有提供）
        if complexity_score is None:
            complexity_score = self._compute_complexity_score(x)
        
        # 动态调整top_k
        k_ratio = self.k_predictor(x.mean(dim=1))  # [batch_size, 1]
        dynamic_k = torch.clamp(
            (k_ratio * self.num_experts).int(), 
            min=2, max=min(self.top_k * 2, self.num_experts)
        )
        
        # 门控计算
        gate_logits = self.gating(x.mean(dim=1))  # [batch_size, num_experts]
        gate_probs = F.softmax(gate_logits, dim=-1)
        
        # 批量处理专家选择
        outputs = []
        load_balancing_loss = 0.0
        
        for i in range(batch_size):
            k = dynamic_k[i].item()
            weights, indices = torch.topk(gate_probs[i], k, dim=-1)
            weights = weights / weights.sum()  # 重新归一化
            
            # 计算专家输出
            expert_outputs = []
            for j, expert_idx in enumerate(indices):
                expert_output = self.experts[expert_idx](x[i])
                expert_outputs.append(weights[j] * expert_output)
            
            combined_output = torch.stack(expert_outputs).sum(dim=0)
            outputs.append(combined_output)
            
            # 更新负载统计
            if self.training:
                for expert_idx in indices:
                    self.expert_counts[expert_idx] += 1
                self.total_tokens += seq_len
        
        output = torch.stack(outputs, dim=0)
        
        # 计算负载均衡损失
        if self.training:
            load_balancing_loss = self._compute_load_balancing_loss(gate_probs)
        
        return output, load_balancing_loss

    def _compute_complexity_score(self, x):
        """计算输入复杂度分数"""
        # 基于输入的方差计算复杂度
        variance = torch.var(x, dim=-1).mean(dim=-1)
        return variance

    def _compute_load_balancing_loss(self, gate_probs):
        """计算负载均衡损失"""
        mean_probs = gate_probs.mean(dim=0)
        uniform_prob = 1.0 / self.num_experts
        kl_loss = F.kl_div(
            torch.log(mean_probs + 1e-8), 
            torch.full_like(mean_probs, uniform_prob),
            reduction='batchmean'
        )
        return kl_loss

# 改进的滑动窗口注意力
def create_sliding_window_mask(seq_len, window_size, device=None):
    """创建滑动窗口注意力掩码"""
    if device is None:
        device = torch.device('cpu')
    
    # 创建因果掩码
    causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=device))
    
    # 创建滑动窗口掩码
    window_mask = torch.zeros(seq_len, seq_len, device=device)
    for i in range(seq_len):
        start = max(0, i - window_size + 1)
        end = i + 1
        window_mask[i, start:end] = 1
    
    # 组合掩码
    mask = causal_mask * window_mask
    attention_mask = torch.where(mask == 0, torch.tensor(-float('inf'), device=device), torch.tensor(0.0, device=device))
    
    return attention_mask

class ImprovedGQAAttention(nn.Module):
    def __init__(self, embed_dim, num_heads=8, group_size=2, window_size=4096, dropout=0.1):
        super(ImprovedGQAAttention, self).__init__()
        assert num_heads % group_size == 0, "num_heads must be divisible by group_size"
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_kv_heads = num_heads // group_size
        self.group_size = group_size
        self.window_size = window_size
        self.head_dim = embed_dim // num_heads
        self.scaling = self.head_dim ** -0.5
        
        # 投影矩阵
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, self.num_kv_heads * self.head_dim)
        self.v_proj = nn.Linear(embed_dim, self.num_kv_heads * self.head_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)

    def forward(self, x, attention_mask=None):
        batch_size, seq_len, _ = x.shape
        
        # 投影查询、键、值
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_kv_heads, self.head_dim)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_kv_heads, self.head_dim)
        
        # 转置
        q = q.transpose(1, 2)  # [batch_size, num_heads, seq_len, head_dim]
        
        # 重复键值以匹配查询头数量
        k = torch.repeat_interleave(k, self.group_size, dim=2)
        v = torch.repeat_interleave(v, self.group_size, dim=2)
        k = k.transpose(1, 2)
        v = v.transpose(1, 2)
        
        # 计算注意力分数
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) * self.scaling
        
        # 应用滑动窗口掩码
        window_mask = create_sliding_window_mask(seq_len, self.window_size, device=x.device)
        attn_weights = attn_weights + window_mask
        
        # 应用额外的注意力掩码
        if attention_mask is not None:
            attn_weights = attn_weights + attention_mask
        
        # Softmax和dropout
        attn_probs = F.softmax(attn_weights, dim=-1)
        attn_probs = self.attn_dropout(attn_probs)
        
        # 应用注意力
        attn_output = torch.matmul(attn_probs, v)
        
        # 重塑输出
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, self.embed_dim)
        
        # 输出投影
        attn_output = self.out_proj(attn_output)
        attn_output = self.resid_dropout(attn_output)
        
        return attn_output

# Transformer编码器层
class ImprovedTransformerLayer(nn.Module):
    def __init__(self, embed_dim, num_heads, group_size, window_size, num_experts, top_k, dropout=0.1):
        super(ImprovedTransformerLayer, self).__init__()
        
        self.self_attn = ImprovedGQAAttention(embed_dim, num_heads, group_size, window_size, dropout)
        self.moe = ImprovedDynamicMoE(num_experts, top_k, embed_dim)
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, attention_mask=None):
        # 自注意力
        residual = x
        x = self.norm1(x)
        attn_output = self.self_attn(x, attention_mask)
        x = residual + self.dropout(attn_output)
        
        # MoE层
        residual = x
        x = self.norm2(x)
        moe_output, load_balancing_loss = self.moe(x)
        x = residual + self.dropout(moe_output)
        
        return x, load_balancing_loss

# 完整的动态MoE Transformer模型
class ImprovedDynamicMoETransformer(nn.Module):
    def __init__(self, vocab_size, embed_dim, num_heads, group_size, window_size, 
                 num_experts, top_k, num_layers, dropout=0.1):
        super(ImprovedDynamicMoETransformer, self).__init__()
        
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.pos_embedding = nn.Parameter(torch.randn(1, 8192, embed_dim) * 0.02)  # 支持长序列
        
        self.layers = nn.ModuleList([
            ImprovedTransformerLayer(embed_dim, num_heads, group_size, window_size, 
                                   num_experts, top_k, dropout)
            for _ in range(num_layers)
        ])
        
        self.norm = nn.LayerNorm(embed_dim)
        self.fc_out = nn.Linear(embed_dim, vocab_size)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, attention_mask=None):
        batch_size, seq_len = x.shape
        
        # 嵌入
        x = self.embedding(x)
        
        # 位置编码
        if seq_len <= self.pos_embedding.size(1):
            x = x + self.pos_embedding[:, :seq_len, :]
        else:
            # 对于超长序列，使用插值
            pos_emb = F.interpolate(
                self.pos_embedding.transpose(1, 2), 
                size=seq_len, 
                mode='linear', 
                align_corners=False
            ).transpose(1, 2)
            x = x + pos_emb
        
        x = self.dropout(x)
        
        # 通过所有层
        total_load_balancing_loss = 0.0
        for layer in self.layers:
            x, load_balancing_loss = layer(x, attention_mask)
            total_load_balancing_loss += load_balancing_loss
        
        # 最终归一化和输出
        x = self.norm(x)
        x = self.fc_out(x)
        
        return x, total_load_balancing_loss

# 简化的PPO训练器
class SimplePPOTrainer:
    def __init__(self, model, lr=3e-4):
        self.model = model

        # 收集门控网络参数
        self.gate_params = []
        for layer in model.layers:
            if hasattr(layer, 'moe'):
                # 确保参数需要梯度
                for param in layer.moe.gating.parameters():
                    param.requires_grad = True
                    self.gate_params.append(param)
                for param in layer.moe.k_predictor.parameters():
                    param.requires_grad = True
                    self.gate_params.append(param)

        if not self.gate_params:
            print("警告: 没有找到门控网络参数，PPO训练器将不会更新任何参数")

        self.optimizer = optim.Adam(self.gate_params, lr=lr) if self.gate_params else None
        self.rewards_history = []

    def update(self, reward):
        if self.optimizer is None:
            # 如果没有参数可优化，只记录奖励
            if isinstance(reward, torch.Tensor):
                self.rewards_history.append(reward.item())
            else:
                self.rewards_history.append(reward)
            return

        if isinstance(reward, (int, float)):
            reward = torch.tensor(reward, dtype=torch.float32, requires_grad=True)
        elif isinstance(reward, torch.Tensor) and not reward.requires_grad:
            reward = reward.clone().detach().requires_grad_(True)

        # 创建一个简单的策略损失
        policy_loss = -reward

        # 只有当损失需要梯度时才进行反向传播
        if policy_loss.requires_grad:
            self.optimizer.zero_grad()
            policy_loss.backward(retain_graph=True)

            # 检查是否有梯度
            has_grad = any(param.grad is not None for param in self.gate_params)
            if has_grad:
                torch.nn.utils.clip_grad_norm_(self.gate_params, max_norm=1.0)
                self.optimizer.step()

        self.rewards_history.append(reward.item() if isinstance(reward, torch.Tensor) else reward)

# 示例使用
if __name__ == "__main__":
    # 模型参数
    vocab_size = 30522
    embed_dim = 768
    num_heads = 8
    group_size = 2
    window_size = 2048  # 减小窗口大小以节省内存
    num_experts = 64    # 减少专家数量
    top_k = 4
    num_layers = 6
    
    # 创建模型
    model = ImprovedDynamicMoETransformer(
        vocab_size, embed_dim, num_heads, group_size, window_size,
        num_experts, top_k, num_layers
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print("改进的动态MoE模型创建成功！")
    
    # 测试前向传播
    batch_size = 2
    seq_len = 128
    test_input = torch.randint(0, vocab_size, (batch_size, seq_len))
    
    with torch.no_grad():
        output, load_loss = model(test_input)
        print(f"输出形状: {output.shape}")
        print(f"负载均衡损失: {load_loss.item():.4f}")
