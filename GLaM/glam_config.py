"""
GLaM (通用语言模型) 配置
基于论文: "GLaM: Efficient Scaling of Language Models with Mixture-of-Experts"
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import torch


@dataclass
class GLaMConfig:
    """GLaM模型配置类"""

    # 模型架构
    vocab_size: int = 256000  # GLaM使用大词汇表
    hidden_size: int = 4096   # 隐藏维度
    num_layers: int = 32      # transformer层数
    num_attention_heads: int = 32  # 注意力头数
    intermediate_size: int = 16384  # FFN中间层大小

    # MoE特定参数
    num_experts: int = 64     # 每个MoE层的专家数量
    top_k: int = 2           # GLaM使用top-2路由
    expert_capacity_factor: float = 1.25  # 负载均衡的容量因子
    moe_layers: list = None  # 哪些层使用MoE (None表示所有层)

    # 路由参数
    routing_algorithm: str = "token_choice"  # "token_choice" 或 "expert_choice"
    load_balancing_weight: float = 0.01     # 辅助损失权重
    router_z_loss_weight: float = 0.001     # 路由器z损失权重

    # 注意力参数
    max_position_embeddings: int = 2048  # 最大位置编码
    attention_dropout: float = 0.1       # 注意力dropout
    hidden_dropout: float = 0.1          # 隐藏层dropout

    # 训练参数
    initializer_range: float = 0.02  # 初始化范围
    layer_norm_eps: float = 1e-5     # 层归一化epsilon
    use_cache: bool = True           # 是否使用缓存

    # 输出控制
    output_attentions: bool = False      # 是否输出注意力权重
    output_hidden_states: bool = False   # 是否输出隐藏状态

    # 效率优化
    use_gradient_checkpointing: bool = False  # 是否使用梯度检查点
    use_flash_attention: bool = False         # 是否使用flash attention

    # 模型并行
    expert_parallel_size: int = 1  # 专家并行的设备数量
    tensor_parallel_size: int = 1  # 张量并行的设备数量

    def __post_init__(self):
        if self.moe_layers is None:
            # 默认情况下，从第1层开始每隔一层使用MoE
            self.moe_layers = list(range(1, self.num_layers, 2))

        # 验证配置
        assert self.hidden_size % self.num_attention_heads == 0
        assert self.top_k <= self.num_experts
        assert self.routing_algorithm in ["token_choice", "expert_choice"]


@dataclass
class GLaMTrainingConfig:
    """GLaM训练配置"""

    # 优化器
    learning_rate: float = 1e-4  # 学习率
    weight_decay: float = 0.1    # 权重衰减
    beta1: float = 0.9           # Adam beta1
    beta2: float = 0.95          # Adam beta2
    eps: float = 1e-8            # Adam epsilon

    # 学习率调度
    warmup_steps: int = 2000     # 预热步数
    max_steps: int = 500000      # 最大训练步数
    lr_decay_style: str = "cosine"  # "linear", "cosine", "constant"
    min_lr_ratio: float = 0.1    # 最小学习率比例

    # 训练动态
    batch_size: int = 512        # 批大小
    sequence_length: int = 2048  # 序列长度
    gradient_accumulation_steps: int = 1  # 梯度累积步数
    max_grad_norm: float = 1.0   # 最大梯度范数

    # 正则化
    dropout: float = 0.1         # dropout率
    attention_dropout: float = 0.1  # 注意力dropout率

    # 检查点
    save_interval: int = 1000    # 保存间隔
    eval_interval: int = 100     # 评估间隔
    log_interval: int = 10       # 日志间隔

    # 混合精度
    fp16: bool = False           # 是否使用FP16
    bf16: bool = True            # 是否使用BF16

    # 数据
    data_path: str = ""          # 数据路径
    tokenizer_path: str = ""     # 分词器路径

    # 分布式训练
    distributed: bool = False    # 是否分布式训练
    local_rank: int = -1         # 本地rank
    world_size: int = 1          # 世界大小


class GLaMModelSizes:
    """论文中预定义的GLaM模型大小"""

    @staticmethod
    def get_config(model_size: str) -> GLaMConfig:
        """获取特定模型大小的配置"""

        configs = {
            "8B": GLaMConfig(
                hidden_size=4096,
                num_layers=32,
                num_attention_heads=32,
                intermediate_size=16384,
                num_experts=64,
                top_k=2
            ),
            "64B": GLaMConfig(
                hidden_size=8192,
                num_layers=32,
                num_attention_heads=64,
                intermediate_size=32768,
                num_experts=64,
                top_k=2
            ),
            "137B": GLaMConfig(
                hidden_size=12288,
                num_layers=40,
                num_attention_heads=96,
                intermediate_size=49152,
                num_experts=64,
                top_k=2
            ),
            "1.2T": GLaMConfig(
                hidden_size=20480,
                num_layers=64,
                num_attention_heads=128,
                intermediate_size=81920,
                num_experts=64,
                top_k=2
            ),
            # 用于测试的较小配置
            "test": GLaMConfig(
                hidden_size=512,
                num_layers=6,
                num_attention_heads=8,
                intermediate_size=2048,
                num_experts=8,
                top_k=2,
                vocab_size=10000,
                max_position_embeddings=512
            ),
            "small": GLaMConfig(
                hidden_size=1024,
                num_layers=12,
                num_attention_heads=16,
                intermediate_size=4096,
                num_experts=16,
                top_k=2,
                vocab_size=32000,
                max_position_embeddings=1024
            )
        }

        if model_size not in configs:
            raise ValueError(f"未知模型大小: {model_size}. 可用选项: {list(configs.keys())}")

        return configs[model_size]


def create_glam_config(
    model_size: str = "test",
    custom_config: Optional[Dict[str, Any]] = None
) -> GLaMConfig:
    """创建GLaM配置，支持可选的自定义设置"""

    config = GLaMModelSizes.get_config(model_size)

    if custom_config:
        for key, value in custom_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                raise ValueError(f"未知配置参数: {key}")

    return config


# 使用示例
if __name__ == "__main__":
    # 创建测试配置
    config = create_glam_config("test")
    print("测试GLaM配置:")
    print(f"  隐藏维度: {config.hidden_size}")
    print(f"  层数: {config.num_layers}")
    print(f"  专家数: {config.num_experts}")
    print(f"  MoE层: {config.moe_layers}")

    # 创建自定义配置
    custom_config = create_glam_config("small", {
        "num_experts": 32,
        "top_k": 4,
        "routing_algorithm": "expert_choice"
    })
    print(f"\n自定义配置 - 专家数: {custom_config.num_experts}, Top-k: {custom_config.top_k}")
