"""
GLaM (Generalist Language Model) Configuration
Based on the paper: "GLaM: Efficient Scaling of Language Models with Mixture-of-Experts"
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import torch


@dataclass
class GLaMConfig:
    """Configuration class for GLaM model"""
    
    # Model architecture
    vocab_size: int = 256000  # GLaM uses large vocabulary
    hidden_size: int = 4096   # Hidden dimension
    num_layers: int = 32      # Number of transformer layers
    num_attention_heads: int = 32
    intermediate_size: int = 16384  # FFN intermediate size
    
    # MoE specific parameters
    num_experts: int = 64     # Number of experts per MoE layer
    top_k: int = 2           # GLaM uses top-2 routing
    expert_capacity_factor: float = 1.25  # Capacity factor for load balancing
    moe_layers: list = None  # Which layers use MoE (None means all)
    
    # Routing parameters
    routing_algorithm: str = "token_choice"  # "token_choice" or "expert_choice"
    load_balancing_weight: float = 0.01     # Weight for auxiliary loss
    router_z_loss_weight: float = 0.001     # Router z-loss weight
    
    # Attention parameters
    max_position_embeddings: int = 2048
    attention_dropout: float = 0.1
    hidden_dropout: float = 0.1
    
    # Training parameters
    initializer_range: float = 0.02
    layer_norm_eps: float = 1e-5
    use_cache: bool = True
    
    # Efficiency optimizations
    use_gradient_checkpointing: bool = False
    use_flash_attention: bool = False
    
    # Model parallelism
    expert_parallel_size: int = 1  # Number of devices for expert parallelism
    tensor_parallel_size: int = 1  # Number of devices for tensor parallelism
    
    def __post_init__(self):
        if self.moe_layers is None:
            # By default, use MoE in every other layer starting from layer 1
            self.moe_layers = list(range(1, self.num_layers, 2))
        
        # Validate configuration
        assert self.hidden_size % self.num_attention_heads == 0
        assert self.top_k <= self.num_experts
        assert self.routing_algorithm in ["token_choice", "expert_choice"]


@dataclass
class GLaMTrainingConfig:
    """Training configuration for GLaM"""
    
    # Optimization
    learning_rate: float = 1e-4
    weight_decay: float = 0.1
    beta1: float = 0.9
    beta2: float = 0.95
    eps: float = 1e-8
    
    # Learning rate schedule
    warmup_steps: int = 2000
    max_steps: int = 500000
    lr_decay_style: str = "cosine"  # "linear", "cosine", "constant"
    min_lr_ratio: float = 0.1
    
    # Training dynamics
    batch_size: int = 512
    sequence_length: int = 2048
    gradient_accumulation_steps: int = 1
    max_grad_norm: float = 1.0
    
    # Regularization
    dropout: float = 0.1
    attention_dropout: float = 0.1
    
    # Checkpointing
    save_interval: int = 1000
    eval_interval: int = 100
    log_interval: int = 10
    
    # Mixed precision
    fp16: bool = False
    bf16: bool = True
    
    # Data
    data_path: str = ""
    tokenizer_path: str = ""
    
    # Distributed training
    distributed: bool = False
    local_rank: int = -1
    world_size: int = 1


class GLaMModelSizes:
    """Predefined GLaM model sizes from the paper"""
    
    @staticmethod
    def get_config(model_size: str) -> GLaMConfig:
        """Get configuration for specific model size"""
        
        configs = {
            "8B": GLaMConfig(
                hidden_size=4096,
                num_layers=32,
                num_attention_heads=32,
                intermediate_size=16384,
                num_experts=64,
                top_k=2
            ),
            "64B": GLaMConfig(
                hidden_size=8192,
                num_layers=32,
                num_attention_heads=64,
                intermediate_size=32768,
                num_experts=64,
                top_k=2
            ),
            "137B": GLaMConfig(
                hidden_size=12288,
                num_layers=40,
                num_attention_heads=96,
                intermediate_size=49152,
                num_experts=64,
                top_k=2
            ),
            "1.2T": GLaMConfig(
                hidden_size=20480,
                num_layers=64,
                num_attention_heads=128,
                intermediate_size=81920,
                num_experts=64,
                top_k=2
            ),
            # Smaller configs for testing
            "test": GLaMConfig(
                hidden_size=512,
                num_layers=6,
                num_attention_heads=8,
                intermediate_size=2048,
                num_experts=8,
                top_k=2,
                vocab_size=10000,
                max_position_embeddings=512
            ),
            "small": GLaMConfig(
                hidden_size=1024,
                num_layers=12,
                num_attention_heads=16,
                intermediate_size=4096,
                num_experts=16,
                top_k=2,
                vocab_size=32000,
                max_position_embeddings=1024
            )
        }
        
        if model_size not in configs:
            raise ValueError(f"Unknown model size: {model_size}. Available: {list(configs.keys())}")
        
        return configs[model_size]


def create_glam_config(
    model_size: str = "test",
    custom_config: Optional[Dict[str, Any]] = None
) -> GLaMConfig:
    """Create GLaM configuration with optional customization"""
    
    config = GLaMModelSizes.get_config(model_size)
    
    if custom_config:
        for key, value in custom_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                raise ValueError(f"Unknown config parameter: {key}")
    
    return config


# Example usage
if __name__ == "__main__":
    # Create test configuration
    config = create_glam_config("test")
    print("Test GLaM Config:")
    print(f"  Hidden size: {config.hidden_size}")
    print(f"  Num layers: {config.num_layers}")
    print(f"  Num experts: {config.num_experts}")
    print(f"  MoE layers: {config.moe_layers}")
    
    # Create custom configuration
    custom_config = create_glam_config("small", {
        "num_experts": 32,
        "top_k": 4,
        "routing_algorithm": "expert_choice"
    })
    print(f"\nCustom Config - Experts: {custom_config.num_experts}, Top-k: {custom_config.top_k}")
