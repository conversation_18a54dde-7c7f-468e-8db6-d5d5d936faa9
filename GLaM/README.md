# GLaM: 通用语言模型实现

本目录包含基于Google Research论文["GLaM: Efficient Scaling of Language Models with Mixture-of-Experts"](https://arxiv.org/abs/2112.06905)的GLaM（通用语言模型）PyTorch实现。

## 概述

GLaM是一系列语言模型，使用稀疏激活的混合专家（MoE）架构来扩展模型容量，同时保持计算效率。本实现包括：

- **稀疏MoE架构**: 高效的混合专家与top-k路由
- **多种路由算法**: 包括token选择和专家选择路由
- **负载均衡**: 辅助损失确保专家使用均匀
- **旋转位置编码**: RoPE提供更好的位置理解
- **高效训练**: 混合精度和分布式训练支持

## 核心特性

### 🚀 **高效扩展**
- 稀疏激活允许扩展到万亿参数
- 每个token只激活一小部分专家
- 保持每个token的恒定计算成本

### 🎯 **先进路由**
- **Token选择**: 标准MoE，token选择专家
- **专家选择**: 替代路由，专家选择token
- 动态容量管理和负载均衡

### ⚡ **优化训练**
- 混合精度训练（FP16/BF16）
- 梯度检查点节省内存
- 分布式训练支持
- 专家特定学习率

### 📊 **全面分析**
- 专家使用统计和可视化
- 路由模式分析
- 性能基准测试工具

## 文件结构

```
GLaM/
├── glam_config.py      # 模型和训练配置
├── glam_routing.py     # 路由算法（token选择、专家选择）
├── glam_moe.py         # 混合专家实现
├── glam_model.py       # 主要GLaM模型架构
├── glam_trainer.py     # 训练工具和优化
├── glam_demo.py        # 演示和测试工具
└── README.md           # 本文件
```

## 快速开始

### 1. 基本使用

```python
from glam_config import create_glam_config
from glam_model import GLaMModel
import torch

# 创建测试模型
config = create_glam_config("test")
model = GLaMModel(config)

# 前向传播
input_ids = torch.randint(0, config.vocab_size, (2, 128))
outputs = model(input_ids=input_ids)
hidden_states, _, _, _, aux_losses = outputs

print(f"输出形状: {hidden_states.shape}")
print(f"辅助损失: {aux_losses}")
```

### 2. 运行演示

```bash
cd GLaM
python glam_demo.py
```

这将运行一个全面的演示，包括：
- 前向传播测试
- 专家路由分析
- 推理基准测试
- 路由算法比较
- 专家使用可视化

### 3. 模型配置

可用的模型大小：

| 模型大小 | 参数量 | 专家数 | 层数 | 隐藏维度 |
|----------|--------|--------|------|----------|
| test     | ~10M   | 8      | 6    | 512      |
| small    | ~100M  | 16     | 12   | 1024     |
| 8B       | ~8B    | 64     | 32   | 4096     |
| 64B      | ~64B   | 64     | 32   | 8192     |
| 137B     | ~137B  | 64     | 40   | 12288    |
| 1.2T     | ~1.2T  | 64     | 64   | 20480    |

### 4. 自定义配置

```python
from glam_config import create_glam_config

# 创建自定义配置
config = create_glam_config("small", {
    "num_experts": 32,
    "top_k": 4,
    "routing_algorithm": "expert_choice",
    "load_balancing_weight": 0.02
})
```

## 架构详情

### 混合专家（MoE）

GLaM使用MoE层替换transformer块中的传统FFN层：

```python
# MoE层在选定层中替换FFN
if layer_idx in config.moe_layers:
    self.mlp = GLaMSparseMoE(
        hidden_size=config.hidden_size,
        intermediate_size=config.intermediate_size,
        num_experts=config.num_experts,
        top_k=config.top_k,
        routing_algorithm=config.routing_algorithm
    )
```

### 路由算法

#### Token选择路由
- 每个token被路由到top-k个专家
- Switch Transformer中使用的标准MoE方法
- 通过辅助损失实现负载均衡

#### 专家选择路由
- 每个专家基于亲和度选择top token
- 更好的负载均衡特性
- 在分布式设置中减少通信

### 负载均衡

多种机制确保专家使用均匀：

1. **负载均衡损失**: 鼓励专家选择均匀
2. **路由器Z损失**: 保持路由器logits稳定
3. **容量因子**: 动态控制专家容量

## 训练

### 基本训练设置

```python
from glam_trainer import GLaMTrainer
from glam_config import GLaMTrainingConfig

# 训练配置
training_config = GLaMTrainingConfig(
    learning_rate=1e-4,
    batch_size=32,
    max_steps=100000,
    warmup_steps=2000,
    fp16=True
)

# 创建训练器
trainer = GLaMTrainer(
    model=model,
    config=training_config,
    train_dataloader=train_loader,
    val_dataloader=val_loader
)

# 开始训练
trainer.train()
```

### 高级特性

- **混合精度**: 自动FP16/BF16训练
- **梯度检查点**: 内存高效训练
- **专家特定学习率**: 专家参数使用不同学习率
- **分布式训练**: 使用DDP的多GPU支持

## 性能分析

### 专家使用统计

```python
# 获取专家使用统计
stats = moe_layer.get_expert_usage_stats()
print(f"专家使用熵: {stats['usage_entropy']}")
print(f"负载均衡: {stats['usage_std']}")
```

### 基准测试

```python
from glam_demo import GLaMDemo

demo = GLaMDemo("test")
benchmark_results = demo.benchmark_inference([64, 128, 256, 512])
```

## 关键实现细节

### 1. 高效专家计算

```python
# 批处理提高效率
for expert_idx in range(num_experts):
    expert_tokens = tokens_for_expert[expert_idx]
    if expert_tokens.size(0) > 0:
        expert_output = self.experts[expert_idx](expert_tokens)
        outputs[expert_idx] = expert_output
```

### 2. 动态容量管理

```python
# 基于负载的专家容量
expert_capacity = int(total_tokens * capacity_factor / num_experts)
```

### 3. 辅助损失计算

```python
# 负载均衡损失
lb_loss = num_experts * torch.sum(expert_fractions * mean_probs)

# 路由器z损失保持稳定性
z_loss = torch.mean(torch.logsumexp(router_logits, dim=-1) ** 2)
```

## 与原论文的比较

本实现紧密遵循GLaM论文，并进行了一些实用性改进：

✅ **已实现特性:**
- 带top-k路由的稀疏MoE架构
- 负载均衡机制
- 专家选择路由（新颖贡献）
- RoPE位置编码
- 混合精度训练

🔄 **改进适配:**
- 简化的模型并行（专注单设备）
- 额外的路由算法用于比较
- 增强的调试和可视化工具
- 便于实验的模块化设计

## 性能优化建议

1. **内存优化**:
   - 对大模型使用梯度检查点
   - 启用混合精度训练
   - 根据序列长度调整批大小

2. **训练稳定性**:
   - 专家使用较低的学习率开始
   - 监控负载均衡损失
   - 对路由器参数使用预热

3. **推理优化**:
   - 缓存键值对用于生成
   - 使用专家选择路由获得更好的负载均衡
   - 将相似序列长度批处理在一起

## 引用

如果您使用此实现，请引用原始GLaM论文：

```bibtex
@article{du2021glam,
  title={GLaM: Efficient Scaling of Language Models with Mixture-of-Experts},
  author={Du, Nan and Huang, Yanping and Dai, Andrew M and Tong, Simon and Lepikhin, Dmitry and Xu, Yuanzhong and Krikun, Maxim and Zhou, Yanqi and Yu, Adams Wei and Firat, Orhan and others},
  journal={arXiv preprint arXiv:2112.06905},
  year={2021}
}
```

## 许可证

本实现仅供研究和教育目的。商业使用请参考原论文和Google的相关条款。

## 贡献

欢迎贡献！改进方向包括：
- 模型并行实现
- 额外的路由算法
- 优化技术
- 评估基准

---

**注意**: 这是一个专注于演示GLaM概念的研究实现。生产使用请考虑额外的优化和充分测试。
