# GLaM: Generalist Language Model Implementation

This directory contains a PyTorch implementation of GLaM (Generalist Language Model) based on the paper ["GLaM: Efficient Scaling of Language Models with Mixture-of-Experts"](https://arxiv.org/abs/2112.06905) by Google Research.

## Overview

GLaM is a family of language models that uses sparsely activated mixture-of-experts (MoE) architecture to scale model capacity while maintaining computational efficiency. This implementation includes:

- **Sparse MoE Architecture**: Efficient mixture-of-experts with top-k routing
- **Multiple Routing Algorithms**: Both token choice and expert choice routing
- **Load Balancing**: Auxiliary losses to ensure uniform expert utilization
- **Rotary Positional Embedding**: RoPE for better positional understanding
- **Efficient Training**: Mixed precision and distributed training support

## Key Features

### 🚀 **Efficient Scaling**
- Sparse activation allows scaling to trillions of parameters
- Only a fraction of experts are activated per token
- Maintains constant computational cost per token

### 🎯 **Advanced Routing**
- **Token Choice**: Standard MoE where tokens choose experts
- **Expert Choice**: Alternative routing where experts choose tokens
- Dynamic capacity management and load balancing

### ⚡ **Optimized Training**
- Mixed precision training (FP16/BF16)
- Gradient checkpointing for memory efficiency
- Distributed training support
- Expert-specific learning rates

### 📊 **Comprehensive Analysis**
- Expert usage statistics and visualization
- Routing pattern analysis
- Performance benchmarking tools

## File Structure

```
GLaM/
├── glam_config.py      # Model and training configurations
├── glam_routing.py     # Routing algorithms (token choice, expert choice)
├── glam_moe.py         # Mixture-of-experts implementation
├── glam_model.py       # Main GLaM model architecture
├── glam_trainer.py     # Training utilities and optimization
├── glam_demo.py        # Demo and testing utilities
└── README.md           # This file
```

## Quick Start

### 1. Basic Usage

```python
from glam_config import create_glam_config
from glam_model import GLaMModel
import torch

# Create a test model
config = create_glam_config("test")
model = GLaMModel(config)

# Forward pass
input_ids = torch.randint(0, config.vocab_size, (2, 128))
outputs = model(input_ids=input_ids)
hidden_states, _, _, _, aux_losses = outputs

print(f"Output shape: {hidden_states.shape}")
print(f"Auxiliary losses: {aux_losses}")
```

### 2. Run Demo

```bash
cd GLaM
python glam_demo.py
```

This will run a comprehensive demo including:
- Forward pass testing
- Expert routing analysis
- Inference benchmarking
- Routing algorithm comparison
- Expert usage visualization

### 3. Model Configurations

Available model sizes:

| Model Size | Parameters | Experts | Layers | Hidden Size |
|------------|------------|---------|--------|-------------|
| test       | ~10M       | 8       | 6      | 512         |
| small      | ~100M      | 16      | 12     | 1024        |
| 8B         | ~8B        | 64      | 32     | 4096        |
| 64B        | ~64B       | 64      | 32     | 8192        |
| 137B       | ~137B      | 64      | 40     | 12288       |
| 1.2T       | ~1.2T      | 64      | 64     | 20480       |

### 4. Custom Configuration

```python
from glam_config import create_glam_config

# Create custom configuration
config = create_glam_config("small", {
    "num_experts": 32,
    "top_k": 4,
    "routing_algorithm": "expert_choice",
    "load_balancing_weight": 0.02
})
```

## Architecture Details

### Mixture of Experts (MoE)

GLaM uses MoE layers to replace traditional FFN layers in transformer blocks:

```python
# MoE layer replaces FFN in selected layers
if layer_idx in config.moe_layers:
    self.mlp = GLaMSparseMoE(
        hidden_size=config.hidden_size,
        intermediate_size=config.intermediate_size,
        num_experts=config.num_experts,
        top_k=config.top_k,
        routing_algorithm=config.routing_algorithm
    )
```

### Routing Algorithms

#### Token Choice Routing
- Each token is routed to top-k experts
- Standard MoE approach used in Switch Transformer
- Balanced load through auxiliary losses

#### Expert Choice Routing
- Each expert selects top tokens based on affinity
- Better load balancing properties
- Reduced communication in distributed settings

### Load Balancing

Multiple mechanisms ensure uniform expert utilization:

1. **Load Balancing Loss**: Encourages uniform expert selection
2. **Router Z-Loss**: Keeps router logits stable
3. **Capacity Factor**: Controls expert capacity dynamically

## Training

### Basic Training Setup

```python
from glam_trainer import GLaMTrainer
from glam_config import GLaMTrainingConfig

# Training configuration
training_config = GLaMTrainingConfig(
    learning_rate=1e-4,
    batch_size=32,
    max_steps=100000,
    warmup_steps=2000,
    fp16=True
)

# Create trainer
trainer = GLaMTrainer(
    model=model,
    config=training_config,
    train_dataloader=train_loader,
    val_dataloader=val_loader
)

# Start training
trainer.train()
```

### Advanced Features

- **Mixed Precision**: Automatic FP16/BF16 training
- **Gradient Checkpointing**: Memory-efficient training
- **Expert-specific Learning Rates**: Different LR for expert parameters
- **Distributed Training**: Multi-GPU support with DDP

## Performance Analysis

### Expert Usage Statistics

```python
# Get expert usage statistics
stats = moe_layer.get_expert_usage_stats()
print(f"Expert usage entropy: {stats['usage_entropy']}")
print(f"Load balancing: {stats['usage_std']}")
```

### Benchmarking

```python
from glam_demo import GLaMDemo

demo = GLaMDemo("test")
benchmark_results = demo.benchmark_inference([64, 128, 256, 512])
```

## Key Implementation Details

### 1. Efficient Expert Computation

```python
# Batch processing for efficiency
for expert_idx in range(num_experts):
    expert_tokens = tokens_for_expert[expert_idx]
    if expert_tokens.size(0) > 0:
        expert_output = self.experts[expert_idx](expert_tokens)
        outputs[expert_idx] = expert_output
```

### 2. Dynamic Capacity Management

```python
# Expert capacity based on load
expert_capacity = int(total_tokens * capacity_factor / num_experts)
```

### 3. Auxiliary Loss Computation

```python
# Load balancing loss
lb_loss = num_experts * torch.sum(expert_fractions * mean_probs)

# Router z-loss for stability
z_loss = torch.mean(torch.logsumexp(router_logits, dim=-1) ** 2)
```

## Comparison with Original Paper

This implementation closely follows the GLaM paper with some practical adaptations:

✅ **Implemented Features:**
- Sparse MoE architecture with top-k routing
- Load balancing mechanisms
- Expert choice routing (novel contribution)
- RoPE positional embeddings
- Mixed precision training

🔄 **Adaptations:**
- Simplified model parallelism (single-device focus)
- Additional routing algorithms for comparison
- Enhanced debugging and visualization tools
- Modular design for easy experimentation

## Performance Tips

1. **Memory Optimization**:
   - Use gradient checkpointing for large models
   - Enable mixed precision training
   - Adjust batch size based on sequence length

2. **Training Stability**:
   - Start with lower learning rates for experts
   - Monitor load balancing losses
   - Use warmup for router parameters

3. **Inference Optimization**:
   - Cache key-value pairs for generation
   - Use expert choice routing for better load balancing
   - Batch similar sequence lengths together

## Citation

If you use this implementation, please cite the original GLaM paper:

```bibtex
@article{du2021glam,
  title={GLaM: Efficient Scaling of Language Models with Mixture-of-Experts},
  author={Du, Nan and Huang, Yanping and Dai, Andrew M and Tong, Simon and Lepikhin, Dmitry and Xu, Yuanzhong and Krikun, Maxim and Zhou, Yanqi and Yu, Adams Wei and Firat, Orhan and others},
  journal={arXiv preprint arXiv:2112.06905},
  year={2021}
}
```

## License

This implementation is provided for research and educational purposes. Please refer to the original paper and Google's terms for any commercial usage considerations.

## Contributing

Contributions are welcome! Areas for improvement:
- Model parallelism implementation
- Additional routing algorithms
- Optimization techniques
- Evaluation benchmarks

---

**Note**: This is a research implementation focused on demonstrating GLaM concepts. For production use, consider additional optimizations and thorough testing.
