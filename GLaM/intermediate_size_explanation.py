"""
详细解释 intermediate_size 的作用和意义
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np

plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False


def explain_intermediate_size():
    """详细解释 intermediate_size 的概念"""
    
    print("=== intermediate_size 详细解释 ===\n")
    
    # 示例参数
    hidden_size = 512      # 输入和输出的维度
    intermediate_size = 2048  # 中间层的维度
    
    print("1. 维度变化流程:")
    print(f"   输入维度:     {hidden_size}")
    print(f"   中间层维度:   {intermediate_size}")
    print(f"   输出维度:     {hidden_size}")
    print(f"   扩展比例:     {intermediate_size / hidden_size}x\n")
    
    # 创建专家网络
    expert = nn.ModuleDict({
        'gate_proj': nn.Linear(hidden_size, intermediate_size, bias=False),
        'up_proj': nn.Linear(hidden_size, intermediate_size, bias=False),
        'down_proj': nn.Linear(intermediate_size, hidden_size, bias=False)
    })
    
    print("2. 网络结构:")
    print(f"   gate_proj: {hidden_size} → {intermediate_size}")
    print(f"   up_proj:   {hidden_size} → {intermediate_size}")
    print(f"   down_proj: {intermediate_size} → {hidden_size}")
    print()
    
    # 计算参数数量
    gate_params = hidden_size * intermediate_size
    up_params = hidden_size * intermediate_size
    down_params = intermediate_size * hidden_size
    total_params = gate_params + up_params + down_params
    
    print("3. 参数数量分析:")
    print(f"   gate_proj 参数: {gate_params:,}")
    print(f"   up_proj 参数:   {up_params:,}")
    print(f"   down_proj 参数: {down_params:,}")
    print(f"   总参数:         {total_params:,}")
    print()
    
    # 前向传播示例
    batch_size = 2
    seq_len = 10
    x = torch.randn(batch_size, seq_len, hidden_size)
    
    with torch.no_grad():
        # 第一步：扩展到中间维度
        gate = expert['gate_proj'](x)  # [2, 10, 512] → [2, 10, 2048]
        up = expert['up_proj'](x)      # [2, 10, 512] → [2, 10, 2048]
        
        # 第二步：门控机制
        gate_activated = F.gelu(gate)
        intermediate = gate_activated * up  # [2, 10, 2048]
        
        # 第三步：压缩回原维度
        output = expert['down_proj'](intermediate)  # [2, 10, 2048] → [2, 10, 512]
    
    print("4. 前向传播维度变化:")
    print(f"   输入 x:           {list(x.shape)}")
    print(f"   gate_proj(x):     {list(gate.shape)}")
    print(f"   up_proj(x):       {list(up.shape)}")
    print(f"   intermediate:     {list(intermediate.shape)}")
    print(f"   输出:             {list(output.shape)}")
    print()
    
    return expert, x, gate, up, intermediate, output


def visualize_dimension_flow():
    """可视化维度变化流程"""
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 定义维度
    hidden_size = 512
    intermediate_size = 2048
    
    # 绘制网络结构图
    layers = ['输入\n(hidden_size)', '门控投影\n(intermediate_size)', 
              '上投影\n(intermediate_size)', '门控融合\n(intermediate_size)', 
              '下投影\n(hidden_size)', '输出\n(hidden_size)']
    
    dimensions = [hidden_size, intermediate_size, intermediate_size, 
                  intermediate_size, hidden_size, hidden_size]
    
    positions = np.arange(len(layers))
    
    # 绘制维度变化
    bars = ax.bar(positions, dimensions, 
                  color=['lightblue', 'orange', 'orange', 'red', 'green', 'lightblue'],
                  alpha=0.7, edgecolor='black', linewidth=1.5)
    
    # 添加维度标签
    for i, (pos, dim) in enumerate(zip(positions, dimensions)):
        ax.text(pos, dim + 50, f'{dim}', ha='center', va='bottom', 
                fontweight='bold', fontsize=12)
    
    # 添加箭头显示数据流
    for i in range(len(positions) - 1):
        ax.annotate('', xy=(positions[i+1] - 0.3, dimensions[i+1]/2), 
                    xytext=(positions[i] + 0.3, dimensions[i]/2),
                    arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))
    
    ax.set_xticks(positions)
    ax.set_xticklabels(layers, rotation=45, ha='right')
    ax.set_ylabel('维度大小')
    ax.set_title('GLaM专家网络中的维度变化流程', fontsize=16, fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    # 添加说明文本
    ax.text(0.02, 0.98, 
            f'扩展比例: {intermediate_size/hidden_size}x\n'
            f'参数增加: {((2*hidden_size*intermediate_size + intermediate_size*hidden_size)/(hidden_size*intermediate_size)-1)*100:.1f}%',
            transform=ax.transAxes, fontsize=12,
            verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="yellow", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('GLaM/dimension_flow.png', dpi=300, bbox_inches='tight')
    plt.show()


def compare_intermediate_sizes():
    """比较不同 intermediate_size 的影响"""
    
    hidden_size = 512
    intermediate_sizes = [1024, 2048, 4096, 8192]  # 不同的中间层大小
    
    print("=== 不同 intermediate_size 的影响 ===\n")
    
    results = []
    
    for inter_size in intermediate_sizes:
        # 计算参数数量
        params = 2 * hidden_size * inter_size + inter_size * hidden_size
        expansion_ratio = inter_size / hidden_size
        
        # 创建专家网络
        expert = nn.ModuleDict({
            'gate_proj': nn.Linear(hidden_size, inter_size, bias=False),
            'up_proj': nn.Linear(hidden_size, inter_size, bias=False),
            'down_proj': nn.Linear(inter_size, hidden_size, bias=False)
        })
        
        # 测试表达能力（通过输出方差衡量）
        x = torch.randn(100, hidden_size)
        with torch.no_grad():
            gate = expert['gate_proj'](x)
            up = expert['up_proj'](x)
            intermediate = F.gelu(gate) * up
            output = expert['down_proj'](intermediate)
            
            output_variance = output.var().item()
            sparsity = (F.gelu(gate) < 0.1).float().mean().item()
        
        results.append({
            'intermediate_size': inter_size,
            'expansion_ratio': expansion_ratio,
            'parameters': params,
            'output_variance': output_variance,
            'sparsity': sparsity
        })
        
        print(f"intermediate_size = {inter_size}:")
        print(f"  扩展比例:     {expansion_ratio:.1f}x")
        print(f"  参数数量:     {params:,}")
        print(f"  输出方差:     {output_variance:.4f}")
        print(f"  稀疏性:       {sparsity:.3f}")
        print()
    
    # 可视化比较
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('不同 intermediate_size 的影响', fontsize=16, fontweight='bold')
    
    inter_sizes = [r['intermediate_size'] for r in results]
    
    # 参数数量
    params = [r['parameters'] for r in results]
    axes[0, 0].plot(inter_sizes, params, 'o-', linewidth=2, markersize=8)
    axes[0, 0].set_xlabel('intermediate_size')
    axes[0, 0].set_ylabel('参数数量')
    axes[0, 0].set_title('参数数量 vs intermediate_size')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 扩展比例
    ratios = [r['expansion_ratio'] for r in results]
    axes[0, 1].plot(inter_sizes, ratios, 'o-', color='orange', linewidth=2, markersize=8)
    axes[0, 1].set_xlabel('intermediate_size')
    axes[0, 1].set_ylabel('扩展比例')
    axes[0, 1].set_title('扩展比例 vs intermediate_size')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 输出方差（表达能力）
    variances = [r['output_variance'] for r in results]
    axes[1, 0].plot(inter_sizes, variances, 'o-', color='green', linewidth=2, markersize=8)
    axes[1, 0].set_xlabel('intermediate_size')
    axes[1, 0].set_ylabel('输出方差')
    axes[1, 0].set_title('表达能力 vs intermediate_size')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 稀疏性
    sparsities = [r['sparsity'] for r in results]
    axes[1, 1].plot(inter_sizes, sparsities, 'o-', color='red', linewidth=2, markersize=8)
    axes[1, 1].set_xlabel('intermediate_size')
    axes[1, 1].set_ylabel('稀疏性')
    axes[1, 1].set_title('稀疏性 vs intermediate_size')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('GLaM/intermediate_size_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return results


def explain_why_expansion():
    """解释为什么需要维度扩展"""
    
    print("=== 为什么需要维度扩展？ ===\n")
    
    print("1. 表达能力增强:")
    print("   • 更大的中间维度 → 更丰富的特征表示")
    print("   • 类似于'瓶颈'结构，先扩展再压缩")
    print("   • 允许网络学习更复杂的非线性变换\n")
    
    print("2. 门控机制的需要:")
    print("   • 需要足够的维度来实现有效的选择性")
    print("   • 更多的门控单元 → 更精细的信息控制")
    print("   • 增强专家之间的差异化\n")
    
    print("3. 计算效率考虑:")
    print("   • 虽然参数增加，但激活是稀疏的")
    print("   • 门控机制天然提供稀疏性")
    print("   • 实际计算量可能不会线性增长\n")
    
    print("4. 经验法则:")
    print("   • 通常 intermediate_size = 4 × hidden_size")
    print("   • GLaM中: 4096 → 16384 (4倍扩展)")
    print("   • 平衡表达能力和计算效率\n")


def main():
    """主函数"""
    
    # 详细解释概念
    expert, x, gate, up, intermediate, output = explain_intermediate_size()
    
    # 可视化维度流程
    print("5. 生成维度变化可视化图...")
    visualize_dimension_flow()
    
    # 比较不同大小的影响
    results = compare_intermediate_sizes()
    
    # 解释设计原理
    explain_why_expansion()
    
    print("=== 总结 ===")
    print("intermediate_size 是专家网络中间层的维度大小:")
    print("✓ 不是输入大小，也不是输出大小")
    print("✓ 是网络内部扩展的维度")
    print("✓ 通常是 hidden_size 的 2-8 倍")
    print("✓ 决定了专家的表达能力和参数数量")
    print("✓ 影响门控机制的精细程度")


if __name__ == "__main__":
    main()
