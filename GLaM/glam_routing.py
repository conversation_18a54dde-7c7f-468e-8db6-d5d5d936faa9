"""
GLaM路由算法
实现GLaM论文中描述的token选择和专家选择路由
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import math


class TokenChoiceRouter(nn.Module):
    """
    Token选择路由 - 标准MoE路由，token选择专家
    每个token基于门控分数被路由到top-k个专家
    """

    def __init__(self, hidden_size: int, num_experts: int, top_k: int = 2):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_experts = num_experts
        self.top_k = top_k

        # 门控网络
        self.gate = nn.Linear(hidden_size, num_experts, bias=False)

        # 初始化门控权重
        nn.init.normal_(self.gate.weight, mean=0.0, std=0.02)

    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        参数:
            hidden_states: [batch_size, seq_len, hidden_size]

        返回:
            expert_weights: [batch_size, seq_len, top_k] - 选中专家的权重
            expert_indices: [batch_size, seq_len, top_k] - 选中专家的索引
            router_logits: [batch_size, seq_len, num_experts] - 原始路由器logits
        """
        batch_size, seq_len, hidden_size = hidden_states.shape

        # 展平用于路由
        hidden_flat = hidden_states.view(-1, hidden_size)  # [batch_size * seq_len, hidden_size]

        # 计算路由器logits
        router_logits = self.gate(hidden_flat)  # [batch_size * seq_len, num_experts]
        router_probs = F.softmax(router_logits, dim=-1)

        # 选择top-k专家
        expert_weights, expert_indices = torch.topk(router_probs, self.top_k, dim=-1)

        # 归一化权重
        expert_weights = expert_weights / expert_weights.sum(dim=-1, keepdim=True)

        # 重新整形
        expert_weights = expert_weights.view(batch_size, seq_len, self.top_k)
        expert_indices = expert_indices.view(batch_size, seq_len, self.top_k)
        router_logits = router_logits.view(batch_size, seq_len, self.num_experts)

        return expert_weights, expert_indices, router_logits


class ExpertChoiceRouter(nn.Module):
    """
    专家选择路由 - 替代路由方式，专家选择token
    每个专家基于亲和度分数选择top token
    """

    def __init__(self, hidden_size: int, num_experts: int, capacity_factor: float = 1.25):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_experts = num_experts
        self.capacity_factor = capacity_factor

        # 门控网络
        self.gate = nn.Linear(hidden_size, num_experts, bias=False)

        # 初始化门控权重
        nn.init.normal_(self.gate.weight, mean=0.0, std=0.02)

    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        参数:
            hidden_states: [batch_size, seq_len, hidden_size]

        返回:
            expert_weights: [batch_size, seq_len, num_experts] - 每个专家的权重
            expert_mask: [batch_size, seq_len, num_experts] - 专家分配的二进制掩码
            router_logits: [batch_size, seq_len, num_experts] - 原始路由器logits
        """
        batch_size, seq_len, hidden_size = hidden_states.shape

        # 展平用于路由
        hidden_flat = hidden_states.view(-1, hidden_size)  # [batch_size * seq_len, hidden_size]

        # 计算路由器logits
        router_logits = self.gate(hidden_flat)  # [batch_size * seq_len, num_experts]
        router_probs = F.softmax(router_logits, dim=-1)

        # 计算专家容量
        total_tokens = batch_size * seq_len
        expert_capacity = int(total_tokens * self.capacity_factor / self.num_experts)

        # 专家选择：每个专家选择top token
        expert_weights = torch.zeros_like(router_probs)
        expert_mask = torch.zeros_like(router_probs)

        for expert_idx in range(self.num_experts):
            # 获取该专家的亲和度分数
            expert_scores = router_probs[:, expert_idx]

            # 为该专家选择top token
            if expert_capacity > 0:
                top_values, top_indices = torch.topk(
                    expert_scores,
                    min(expert_capacity, total_tokens),
                    dim=0
                )

                # 将token分配给专家
                expert_weights[top_indices, expert_idx] = top_values
                expert_mask[top_indices, expert_idx] = 1.0

        # 按token归一化权重
        token_weights_sum = expert_weights.sum(dim=-1, keepdim=True)
        expert_weights = expert_weights / (token_weights_sum + 1e-8)

        # 重新整形
        expert_weights = expert_weights.view(batch_size, seq_len, self.num_experts)
        expert_mask = expert_mask.view(batch_size, seq_len, self.num_experts)
        router_logits = router_logits.view(batch_size, seq_len, self.num_experts)

        return expert_weights, expert_mask, router_logits


class LoadBalancingLoss(nn.Module):
    """
    计算负载均衡损失以鼓励专家使用均匀
    """

    def __init__(self, num_experts: int, top_k: int):
        super().__init__()
        self.num_experts = num_experts
        self.top_k = top_k

    def forward(self, router_probs: torch.Tensor, expert_indices: torch.Tensor) -> torch.Tensor:
        """
        参数:
            router_probs: [batch_size, seq_len, num_experts] - 路由器概率
            expert_indices: [batch_size, seq_len, top_k] - 选中的专家索引

        返回:
            load_balancing_loss: 标量张量
        """
        batch_size, seq_len, num_experts = router_probs.shape

        # 计算路由到每个专家的token比例
        expert_counts = torch.zeros(num_experts, device=router_probs.device)
        for i in range(self.top_k):
            expert_counts.scatter_add_(0, expert_indices[:, :, i].flatten(),
                                     torch.ones_like(expert_indices[:, :, i].flatten(), dtype=torch.float))

        expert_fractions = expert_counts / (batch_size * seq_len * self.top_k)

        # 计算每个专家的平均概率
        mean_probs = router_probs.mean(dim=[0, 1])  # [num_experts]

        # 负载均衡损失：鼓励均匀分布
        load_balancing_loss = self.num_experts * torch.sum(expert_fractions * mean_probs)

        return load_balancing_loss


class RouterZLoss(nn.Module):
    """
    路由器Z损失，鼓励路由器logits保持接近零
    有助于训练稳定性
    """

    def forward(self, router_logits: torch.Tensor) -> torch.Tensor:
        """
        参数:
            router_logits: [batch_size, seq_len, num_experts] - 原始路由器logits

        返回:
            z_loss: 标量张量
        """
        # 计算log-sum-exp以保证数值稳定性
        log_z = torch.logsumexp(router_logits, dim=-1)  # [batch_size, seq_len]
        z_loss = torch.mean(log_z ** 2)

        return z_loss


def create_router(
    routing_algorithm: str,
    hidden_size: int,
    num_experts: int,
    top_k: int = 2,
    capacity_factor: float = 1.25
) -> nn.Module:
    """
    根据算法类型创建路由器的工厂函数

    参数:
        routing_algorithm: "token_choice" 或 "expert_choice"
        hidden_size: 隐藏维度大小
        num_experts: 专家数量
        top_k: 路由到的专家数量（用于token选择）
        capacity_factor: 专家容量因子（用于专家选择）

    返回:
        路由器模块
    """
    if routing_algorithm == "token_choice":
        return TokenChoiceRouter(hidden_size, num_experts, top_k)
    elif routing_algorithm == "expert_choice":
        return ExpertChoiceRouter(hidden_size, num_experts, capacity_factor)
    else:
        raise ValueError(f"未知路由算法: {routing_algorithm}")


# 使用示例和测试
if __name__ == "__main__":
    # 测试token选择路由
    print("测试Token选择路由器...")
    hidden_size = 512
    num_experts = 8
    top_k = 2
    batch_size = 2
    seq_len = 10

    router = TokenChoiceRouter(hidden_size, num_experts, top_k)
    hidden_states = torch.randn(batch_size, seq_len, hidden_size)

    expert_weights, expert_indices, router_logits = router(hidden_states)
    print(f"专家权重形状: {expert_weights.shape}")
    print(f"专家索引形状: {expert_indices.shape}")
    print(f"路由器logits形状: {router_logits.shape}")

    # 测试专家选择路由
    print("\n测试专家选择路由器...")
    ec_router = ExpertChoiceRouter(hidden_size, num_experts, capacity_factor=1.25)
    expert_weights_ec, expert_mask_ec, router_logits_ec = ec_router(hidden_states)
    print(f"专家权重形状: {expert_weights_ec.shape}")
    print(f"专家掩码形状: {expert_mask_ec.shape}")

    # 测试负载均衡损失
    print("\n测试负载均衡损失...")
    lb_loss = LoadBalancingLoss(num_experts, top_k)
    router_probs = F.softmax(router_logits, dim=-1)
    loss = lb_loss(router_probs, expert_indices)
    print(f"负载均衡损失: {loss.item():.4f}")

    # 测试路由器z损失
    print("\n测试路由器Z损失...")
    z_loss_fn = RouterZLoss()
    z_loss = z_loss_fn(router_logits)
    print(f"路由器Z损失: {z_loss.item():.4f}")
