"""
GLaM Routing Algorithms
Implements token choice and expert choice routing as described in the GLaM paper
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import math


class TokenChoiceRouter(nn.Module):
    """
    Token Choice Routing - Standard MoE routing where tokens choose experts
    Each token is routed to top-k experts based on gating scores
    """
    
    def __init__(self, hidden_size: int, num_experts: int, top_k: int = 2):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_experts = num_experts
        self.top_k = top_k
        
        # Gating network
        self.gate = nn.Linear(hidden_size, num_experts, bias=False)
        
        # Initialize gate weights
        nn.init.normal_(self.gate.weight, mean=0.0, std=0.02)
    
    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
        
        Returns:
            expert_weights: [batch_size, seq_len, top_k] - weights for selected experts
            expert_indices: [batch_size, seq_len, top_k] - indices of selected experts
            router_logits: [batch_size, seq_len, num_experts] - raw router logits
        """
        batch_size, seq_len, hidden_size = hidden_states.shape
        
        # Flatten for routing
        hidden_flat = hidden_states.view(-1, hidden_size)  # [batch_size * seq_len, hidden_size]
        
        # Compute router logits
        router_logits = self.gate(hidden_flat)  # [batch_size * seq_len, num_experts]
        router_probs = F.softmax(router_logits, dim=-1)
        
        # Select top-k experts
        expert_weights, expert_indices = torch.topk(router_probs, self.top_k, dim=-1)
        
        # Normalize weights
        expert_weights = expert_weights / expert_weights.sum(dim=-1, keepdim=True)
        
        # Reshape back
        expert_weights = expert_weights.view(batch_size, seq_len, self.top_k)
        expert_indices = expert_indices.view(batch_size, seq_len, self.top_k)
        router_logits = router_logits.view(batch_size, seq_len, self.num_experts)
        
        return expert_weights, expert_indices, router_logits


class ExpertChoiceRouter(nn.Module):
    """
    Expert Choice Routing - Alternative routing where experts choose tokens
    Each expert selects top tokens based on affinity scores
    """
    
    def __init__(self, hidden_size: int, num_experts: int, capacity_factor: float = 1.25):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_experts = num_experts
        self.capacity_factor = capacity_factor
        
        # Gating network
        self.gate = nn.Linear(hidden_size, num_experts, bias=False)
        
        # Initialize gate weights
        nn.init.normal_(self.gate.weight, mean=0.0, std=0.02)
    
    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
        
        Returns:
            expert_weights: [batch_size, seq_len, num_experts] - weights for each expert
            expert_mask: [batch_size, seq_len, num_experts] - binary mask for expert assignment
            router_logits: [batch_size, seq_len, num_experts] - raw router logits
        """
        batch_size, seq_len, hidden_size = hidden_states.shape
        
        # Flatten for routing
        hidden_flat = hidden_states.view(-1, hidden_size)  # [batch_size * seq_len, hidden_size]
        
        # Compute router logits
        router_logits = self.gate(hidden_flat)  # [batch_size * seq_len, num_experts]
        router_probs = F.softmax(router_logits, dim=-1)
        
        # Calculate expert capacity
        total_tokens = batch_size * seq_len
        expert_capacity = int(total_tokens * self.capacity_factor / self.num_experts)
        
        # Expert choice: each expert selects top tokens
        expert_weights = torch.zeros_like(router_probs)
        expert_mask = torch.zeros_like(router_probs)
        
        for expert_idx in range(self.num_experts):
            # Get affinity scores for this expert
            expert_scores = router_probs[:, expert_idx]
            
            # Select top tokens for this expert
            if expert_capacity > 0:
                top_values, top_indices = torch.topk(
                    expert_scores, 
                    min(expert_capacity, total_tokens), 
                    dim=0
                )
                
                # Assign tokens to expert
                expert_weights[top_indices, expert_idx] = top_values
                expert_mask[top_indices, expert_idx] = 1.0
        
        # Normalize weights per token
        token_weights_sum = expert_weights.sum(dim=-1, keepdim=True)
        expert_weights = expert_weights / (token_weights_sum + 1e-8)
        
        # Reshape back
        expert_weights = expert_weights.view(batch_size, seq_len, self.num_experts)
        expert_mask = expert_mask.view(batch_size, seq_len, self.num_experts)
        router_logits = router_logits.view(batch_size, seq_len, self.num_experts)
        
        return expert_weights, expert_mask, router_logits


class LoadBalancingLoss(nn.Module):
    """
    Compute load balancing loss to encourage uniform expert utilization
    """
    
    def __init__(self, num_experts: int, top_k: int):
        super().__init__()
        self.num_experts = num_experts
        self.top_k = top_k
    
    def forward(self, router_probs: torch.Tensor, expert_indices: torch.Tensor) -> torch.Tensor:
        """
        Args:
            router_probs: [batch_size, seq_len, num_experts] - router probabilities
            expert_indices: [batch_size, seq_len, top_k] - selected expert indices
        
        Returns:
            load_balancing_loss: scalar tensor
        """
        batch_size, seq_len, num_experts = router_probs.shape
        
        # Compute fraction of tokens routed to each expert
        expert_counts = torch.zeros(num_experts, device=router_probs.device)
        for i in range(self.top_k):
            expert_counts.scatter_add_(0, expert_indices[:, :, i].flatten(), 
                                     torch.ones_like(expert_indices[:, :, i].flatten(), dtype=torch.float))
        
        expert_fractions = expert_counts / (batch_size * seq_len * self.top_k)
        
        # Compute mean probability for each expert
        mean_probs = router_probs.mean(dim=[0, 1])  # [num_experts]
        
        # Load balancing loss: encourage uniform distribution
        load_balancing_loss = self.num_experts * torch.sum(expert_fractions * mean_probs)
        
        return load_balancing_loss


class RouterZLoss(nn.Module):
    """
    Router Z-loss to encourage router logits to stay close to zero
    Helps with training stability
    """
    
    def forward(self, router_logits: torch.Tensor) -> torch.Tensor:
        """
        Args:
            router_logits: [batch_size, seq_len, num_experts] - raw router logits
        
        Returns:
            z_loss: scalar tensor
        """
        # Compute log-sum-exp for numerical stability
        log_z = torch.logsumexp(router_logits, dim=-1)  # [batch_size, seq_len]
        z_loss = torch.mean(log_z ** 2)
        
        return z_loss


def create_router(
    routing_algorithm: str,
    hidden_size: int,
    num_experts: int,
    top_k: int = 2,
    capacity_factor: float = 1.25
) -> nn.Module:
    """
    Factory function to create router based on algorithm type
    
    Args:
        routing_algorithm: "token_choice" or "expert_choice"
        hidden_size: Hidden dimension size
        num_experts: Number of experts
        top_k: Number of experts to route to (for token choice)
        capacity_factor: Expert capacity factor (for expert choice)
    
    Returns:
        Router module
    """
    if routing_algorithm == "token_choice":
        return TokenChoiceRouter(hidden_size, num_experts, top_k)
    elif routing_algorithm == "expert_choice":
        return ExpertChoiceRouter(hidden_size, num_experts, capacity_factor)
    else:
        raise ValueError(f"Unknown routing algorithm: {routing_algorithm}")


# Example usage and testing
if __name__ == "__main__":
    # Test token choice routing
    print("Testing Token Choice Router...")
    hidden_size = 512
    num_experts = 8
    top_k = 2
    batch_size = 2
    seq_len = 10
    
    router = TokenChoiceRouter(hidden_size, num_experts, top_k)
    hidden_states = torch.randn(batch_size, seq_len, hidden_size)
    
    expert_weights, expert_indices, router_logits = router(hidden_states)
    print(f"Expert weights shape: {expert_weights.shape}")
    print(f"Expert indices shape: {expert_indices.shape}")
    print(f"Router logits shape: {router_logits.shape}")
    
    # Test expert choice routing
    print("\nTesting Expert Choice Router...")
    ec_router = ExpertChoiceRouter(hidden_size, num_experts, capacity_factor=1.25)
    expert_weights_ec, expert_mask_ec, router_logits_ec = ec_router(hidden_states)
    print(f"Expert weights shape: {expert_weights_ec.shape}")
    print(f"Expert mask shape: {expert_mask_ec.shape}")
    
    # Test load balancing loss
    print("\nTesting Load Balancing Loss...")
    lb_loss = LoadBalancingLoss(num_experts, top_k)
    router_probs = F.softmax(router_logits, dim=-1)
    loss = lb_loss(router_probs, expert_indices)
    print(f"Load balancing loss: {loss.item():.4f}")
    
    # Test router z-loss
    print("\nTesting Router Z-Loss...")
    z_loss_fn = RouterZLoss()
    z_loss = z_loss_fn(router_logits)
    print(f"Router Z-loss: {z_loss.item():.4f}")
