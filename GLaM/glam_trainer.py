"""
GLaM Training Utilities
Training loop and optimization for GLaM model
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.distributed as dist
from typing import Dict, Any, Optional, Tuple
import time
import logging
import os
from dataclasses import asdict

from glam_config import GLaMConfig, GLaMTrainingConfig
from glam_model import GLaMModel


class GLaMTrainer:
    """GLaM model trainer with MoE-specific optimizations"""
    
    def __init__(
        self,
        model: GLaMModel,
        config: GLaMTrainingConfig,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        device: str = "cuda",
        local_rank: int = -1
    ):
        self.model = model
        self.config = config
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.device = device
        self.local_rank = local_rank
        
        # Setup distributed training
        self.is_distributed = local_rank != -1
        if self.is_distributed:
            self.model = nn.parallel.DistributedDataParallel(
                self.model, device_ids=[local_rank], output_device=local_rank
            )
        
        # Setup optimizer
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        
        # Logging
        self.setup_logging()
        
        # Mixed precision
        self.scaler = torch.cuda.amp.GradScaler() if config.fp16 else None
        self.use_amp = config.fp16 or config.bf16
        self.amp_dtype = torch.bfloat16 if config.bf16 else torch.float16
    
    def _create_optimizer(self) -> optim.Optimizer:
        """Create optimizer with different learning rates for different parameter groups"""
        
        # Separate parameters for different learning rates
        expert_params = []
        non_expert_params = []
        
        for name, param in self.model.named_parameters():
            if 'experts' in name:
                expert_params.append(param)
            else:
                non_expert_params.append(param)
        
        # Different learning rates for expert and non-expert parameters
        param_groups = [
            {
                'params': non_expert_params,
                'lr': self.config.learning_rate,
                'weight_decay': self.config.weight_decay
            },
            {
                'params': expert_params,
                'lr': self.config.learning_rate * 0.1,  # Lower LR for experts
                'weight_decay': self.config.weight_decay * 0.1
            }
        ]
        
        optimizer = optim.AdamW(
            param_groups,
            lr=self.config.learning_rate,
            betas=(self.config.beta1, self.config.beta2),
            eps=self.config.eps,
            weight_decay=self.config.weight_decay
        )
        
        return optimizer
    
    def _create_scheduler(self):
        """Create learning rate scheduler"""
        if self.config.lr_decay_style == "cosine":
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.max_steps,
                eta_min=self.config.learning_rate * self.config.min_lr_ratio
            )
        elif self.config.lr_decay_style == "linear":
            scheduler = optim.lr_scheduler.LinearLR(
                self.optimizer,
                start_factor=1.0,
                end_factor=self.config.min_lr_ratio,
                total_iters=self.config.max_steps
            )
        else:  # constant
            scheduler = optim.lr_scheduler.ConstantLR(self.optimizer, factor=1.0)
        
        return scheduler
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('glam_training.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """Single training step"""
        self.model.train()
        
        # Move batch to device
        input_ids = batch['input_ids'].to(self.device)
        attention_mask = batch.get('attention_mask', None)
        if attention_mask is not None:
            attention_mask = attention_mask.to(self.device)
        
        # Forward pass with mixed precision
        with torch.cuda.amp.autocast(enabled=self.use_amp, dtype=self.amp_dtype):
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                use_cache=False
            )
            
            hidden_states, _, _, _, aux_losses = outputs
            
            # Compute language modeling loss
            shift_logits = hidden_states[..., :-1, :].contiguous()
            shift_labels = input_ids[..., 1:].contiguous()
            
            # Flatten for cross entropy
            shift_logits = shift_logits.view(-1, shift_logits.size(-1))
            shift_labels = shift_labels.view(-1)
            
            # Compute cross entropy loss
            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
            lm_loss = loss_fct(shift_logits, shift_labels)
            
            # Add auxiliary losses
            total_loss = lm_loss
            loss_dict = {'lm_loss': lm_loss.item()}
            
            for loss_name, loss_value in aux_losses.items():
                total_loss += loss_value
                loss_dict[loss_name] = loss_value.item()
            
            loss_dict['total_loss'] = total_loss.item()
        
        # Backward pass
        if self.scaler is not None:
            self.scaler.scale(total_loss).backward()
            self.scaler.unscale_(self.optimizer)
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
            self.optimizer.step()
        
        self.optimizer.zero_grad()
        self.scheduler.step()
        
        return loss_dict
    
    def validate(self) -> Dict[str, float]:
        """Validation loop"""
        if self.val_dataloader is None:
            return {}
        
        self.model.eval()
        total_loss = 0.0
        total_aux_losses = {}
        num_batches = 0
        
        with torch.no_grad():
            for batch in self.val_dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch.get('attention_mask', None)
                if attention_mask is not None:
                    attention_mask = attention_mask.to(self.device)
                
                with torch.cuda.amp.autocast(enabled=self.use_amp, dtype=self.amp_dtype):
                    outputs = self.model(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        use_cache=False
                    )
                    
                    hidden_states, _, _, _, aux_losses = outputs
                    
                    # Compute language modeling loss
                    shift_logits = hidden_states[..., :-1, :].contiguous()
                    shift_labels = input_ids[..., 1:].contiguous()
                    
                    shift_logits = shift_logits.view(-1, shift_logits.size(-1))
                    shift_labels = shift_labels.view(-1)
                    
                    loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
                    lm_loss = loss_fct(shift_logits, shift_labels)
                    
                    total_loss += lm_loss.item()
                    
                    # Accumulate auxiliary losses
                    for loss_name, loss_value in aux_losses.items():
                        if loss_name not in total_aux_losses:
                            total_aux_losses[loss_name] = 0.0
                        total_aux_losses[loss_name] += loss_value.item()
                
                num_batches += 1
        
        # Average losses
        avg_loss = total_loss / num_batches
        avg_aux_losses = {k: v / num_batches for k, v in total_aux_losses.items()}
        
        val_metrics = {'val_loss': avg_loss}
        val_metrics.update({f'val_{k}': v for k, v in avg_aux_losses.items()})
        
        return val_metrics
    
    def train(self):
        """Main training loop"""
        self.logger.info("Starting GLaM training...")
        self.logger.info(f"Training config: {asdict(self.config)}")
        
        start_time = time.time()
        
        for epoch in range(self.config.max_steps // len(self.train_dataloader) + 1):
            self.epoch = epoch
            
            for batch_idx, batch in enumerate(self.train_dataloader):
                if self.global_step >= self.config.max_steps:
                    break
                
                # Training step
                loss_dict = self.train_step(batch)
                self.global_step += 1
                
                # Logging
                if self.global_step % self.config.log_interval == 0:
                    elapsed_time = time.time() - start_time
                    lr = self.scheduler.get_last_lr()[0]
                    
                    log_msg = f"Step {self.global_step}/{self.config.max_steps} | "
                    log_msg += f"LR: {lr:.2e} | "
                    log_msg += f"Time: {elapsed_time:.1f}s | "
                    for k, v in loss_dict.items():
                        log_msg += f"{k}: {v:.4f} | "
                    
                    self.logger.info(log_msg)
                
                # Validation
                if self.global_step % self.config.eval_interval == 0:
                    val_metrics = self.validate()
                    if val_metrics:
                        val_log = f"Validation - Step {self.global_step} | "
                        for k, v in val_metrics.items():
                            val_log += f"{k}: {v:.4f} | "
                        self.logger.info(val_log)
                        
                        # Save best model
                        if val_metrics['val_loss'] < self.best_val_loss:
                            self.best_val_loss = val_metrics['val_loss']
                            self.save_checkpoint('best_model.pt')
                
                # Save checkpoint
                if self.global_step % self.config.save_interval == 0:
                    self.save_checkpoint(f'checkpoint_step_{self.global_step}.pt')
            
            if self.global_step >= self.config.max_steps:
                break
        
        self.logger.info("Training completed!")
        self.save_checkpoint('final_model.pt')
    
    def save_checkpoint(self, filename: str):
        """Save model checkpoint"""
        if self.local_rank in [-1, 0]:  # Only save on main process
            checkpoint = {
                'model_state_dict': self.model.module.state_dict() if self.is_distributed else self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict(),
                'global_step': self.global_step,
                'epoch': self.epoch,
                'best_val_loss': self.best_val_loss,
                'config': asdict(self.config)
            }
            
            if self.scaler is not None:
                checkpoint['scaler_state_dict'] = self.scaler.state_dict()
            
            torch.save(checkpoint, filename)
            self.logger.info(f"Checkpoint saved: {filename}")
    
    def load_checkpoint(self, filename: str):
        """Load model checkpoint"""
        checkpoint = torch.load(filename, map_location=self.device)
        
        if self.is_distributed:
            self.model.module.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.global_step = checkpoint['global_step']
        self.epoch = checkpoint['epoch']
        self.best_val_loss = checkpoint['best_val_loss']
        
        if self.scaler is not None and 'scaler_state_dict' in checkpoint:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
        
        self.logger.info(f"Checkpoint loaded: {filename}")


# Example usage
if __name__ == "__main__":
    from glam_config import create_glam_config
    
    # Create model and config
    model_config = create_glam_config("test")
    training_config = GLaMTrainingConfig(
        learning_rate=1e-4,
        batch_size=4,
        max_steps=1000,
        log_interval=10,
        eval_interval=100,
        save_interval=500
    )
    
    model = GLaMModel(model_config)
    
    print("GLaM trainer setup completed!")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
