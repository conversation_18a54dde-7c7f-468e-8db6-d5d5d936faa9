"""
专家网络架构对比分析
展示不同专家网络设计的差异和优势
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np


class TraditionalFFN(nn.Module):
    """传统的前馈网络"""
    
    def __init__(self, hidden_size: int, intermediate_size: int):
        super().__init__()
        self.linear1 = nn.Linear(hidden_size, intermediate_size)
        self.linear2 = nn.Linear(intermediate_size, hidden_size)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        # 传统FFN: Linear -> GELU -> Dropout -> Linear
        intermediate = F.gelu(self.linear1(x))
        intermediate = self.dropout(intermediate)
        output = self.linear2(intermediate)
        return output


class GLUExpert(nn.Module):
    """GLU风格的专家网络 (GLaM使用的)"""
    
    def __init__(self, hidden_size: int, intermediate_size: int):
        super().__init__()
        self.gate_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
        self.up_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
        self.down_proj = nn.Linear(intermediate_size, hidden_size, bias=False)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        # GLU风格: gate_proj(x) * GELU(up_proj(x))
        gate = self.gate_proj(x)
        up = self.up_proj(x)
        intermediate = F.gelu(gate) * up  # 关键的门控机制
        intermediate = self.dropout(intermediate)
        output = self.down_proj(intermediate)
        return output


class SwiGLUExpert(nn.Module):
    """SwiGLU专家网络 (另一种GLU变体)"""
    
    def __init__(self, hidden_size: int, intermediate_size: int):
        super().__init__()
        self.gate_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
        self.up_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
        self.down_proj = nn.Linear(intermediate_size, hidden_size, bias=False)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        # SwiGLU: gate_proj(x) * Swish(up_proj(x))
        gate = self.gate_proj(x)
        up = self.up_proj(x)
        intermediate = F.silu(gate) * up  # 使用Swish激活
        intermediate = self.dropout(intermediate)
        output = self.down_proj(intermediate)
        return output


def analyze_expert_behavior():
    """分析不同专家网络的行为特征"""
    
    hidden_size = 512
    intermediate_size = 2048
    batch_size = 8
    seq_len = 32
    
    # 创建不同类型的专家
    traditional_ffn = TraditionalFFN(hidden_size, intermediate_size)
    glu_expert = GLUExpert(hidden_size, intermediate_size)
    swiglu_expert = SwiGLUExpert(hidden_size, intermediate_size)
    
    # 生成测试输入
    x = torch.randn(batch_size, seq_len, hidden_size)
    
    print("=== 专家网络架构对比分析 ===\n")
    
    # 1. 参数数量对比
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters())
    
    traditional_params = count_parameters(traditional_ffn)
    glu_params = count_parameters(glu_expert)
    swiglu_params = count_parameters(swiglu_expert)
    
    print("1. 参数数量对比:")
    print(f"   传统FFN:     {traditional_params:,} 参数")
    print(f"   GLU专家:     {glu_params:,} 参数 (+{glu_params-traditional_params:,})")
    print(f"   SwiGLU专家:  {swiglu_params:,} 参数 (+{swiglu_params-traditional_params:,})")
    print(f"   GLU参数增加: {(glu_params/traditional_params-1)*100:.1f}%\n")
    
    # 2. 前向传播分析
    with torch.no_grad():
        traditional_out = traditional_ffn(x)
        glu_out = glu_expert(x)
        swiglu_out = swiglu_expert(x)
    
    print("2. 输出特征分析:")
    print(f"   传统FFN输出范围:    [{traditional_out.min():.3f}, {traditional_out.max():.3f}]")
    print(f"   GLU专家输出范围:    [{glu_out.min():.3f}, {glu_out.max():.3f}]")
    print(f"   SwiGLU专家输出范围: [{swiglu_out.min():.3f}, {swiglu_out.max():.3f}]")
    
    print(f"   传统FFN输出标准差:  {traditional_out.std():.3f}")
    print(f"   GLU专家输出标准差:  {glu_out.std():.3f}")
    print(f"   SwiGLU专家输出标准差: {swiglu_out.std():.3f}\n")
    
    # 3. 门控机制分析 (仅对GLU类型)
    print("3. GLU门控机制分析:")
    
    with torch.no_grad():
        # 分析GLU专家的门控行为
        gate = glu_expert.gate_proj(x)
        up = glu_expert.up_proj(x)
        gate_activated = F.gelu(gate)
        
        # 计算门控的选择性
        gate_sparsity = (gate_activated < 0.1).float().mean()
        gate_activation_mean = gate_activated.mean()
        
        print(f"   门控稀疏性 (激活<0.1): {gate_sparsity:.3f}")
        print(f"   门控平均激活值:       {gate_activation_mean:.3f}")
        print(f"   门控标准差:           {gate_activated.std():.3f}")
    
    return {
        'traditional_params': traditional_params,
        'glu_params': glu_params,
        'traditional_out': traditional_out,
        'glu_out': glu_out,
        'gate_sparsity': gate_sparsity.item()
    }


def visualize_gating_mechanism():
    """可视化门控机制的工作原理"""
    
    print("\n4. 门控机制可视化分析:")
    
    hidden_size = 64  # 较小的尺寸便于可视化
    intermediate_size = 256
    
    glu_expert = GLUExpert(hidden_size, intermediate_size)
    
    # 创建不同类型的输入模式
    inputs = {
        '随机输入': torch.randn(1, 10, hidden_size),
        '正值输入': torch.abs(torch.randn(1, 10, hidden_size)),
        '稀疏输入': torch.randn(1, 10, hidden_size) * (torch.rand(1, 10, hidden_size) > 0.7)
    }
    
    with torch.no_grad():
        for input_name, x in inputs.items():
            gate = glu_expert.gate_proj(x)
            up = glu_expert.up_proj(x)
            gate_activated = F.gelu(gate)
            
            # 分析门控效果
            gating_effect = gate_activated * up
            
            print(f"   {input_name}:")
            print(f"     门控激活率: {(gate_activated > 0.1).float().mean():.3f}")
            print(f"     信息保留率: {(gating_effect.abs() > 0.01).float().mean():.3f}")
            print(f"     输出动态范围: {gating_effect.std():.3f}")


def explain_design_choices():
    """解释GLaM专家网络的设计选择"""
    
    print("\n=== GLaM专家网络设计原理解释 ===\n")
    
    print("1. 为什么使用GLU风格？")
    print("   ✓ 选择性信息传递: 门控机制允许网络学习哪些信息重要")
    print("   ✓ 增强表达能力: 两个线性变换提供更丰富的特征空间")
    print("   ✓ 更好的梯度流: 乘法操作提供额外的梯度路径")
    print("   ✓ 专家差异化: 不同专家可以学习不同的门控模式\n")
    
    print("2. 为什么不使用bias？")
    print("   ✓ 减少参数数量: 在大规模模型中节省内存")
    print("   ✓ 更好的泛化: 减少过拟合风险")
    print("   ✓ 数值稳定性: 避免bias引入的偏移\n")
    
    print("3. 为什么使用GELU而不是ReLU？")
    print("   ✓ 平滑激活: GELU提供更平滑的梯度")
    print("   ✓ 概率解释: GELU有明确的概率意义")
    print("   ✓ 更好性能: 在大多数NLP任务上表现更好\n")
    
    print("4. 专家网络在MoE中的作用：")
    print("   ✓ 专业化处理: 每个专家专注于特定类型的输入")
    print("   ✓ 容量扩展: 增加模型容量而不增加计算量")
    print("   ✓ 动态选择: 根据输入动态选择最合适的专家")


def main():
    """主函数"""
    
    # 运行分析
    results = analyze_expert_behavior()
    
    # 可视化门控机制
    visualize_gating_mechanism()
    
    # 解释设计选择
    explain_design_choices()
    
    print(f"\n=== 总结 ===")
    print(f"GLaM使用GLU风格的专家网络是经过深思熟虑的设计选择：")
    print(f"• 虽然参数增加了{(results['glu_params']/results['traditional_params']-1)*100:.1f}%")
    print(f"• 但获得了更强的表达能力和更好的专家差异化")
    print(f"• 门控机制提供了{results['gate_sparsity']:.1%}的天然稀疏性")
    print(f"• 这种设计在大规模语言模型中被证明是有效的")


if __name__ == "__main__":
    main()
