"""
GLaM Demo and Testing
Demonstrates GLaM model capabilities and provides testing utilities
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import time
from typing import List, Dict, Any, Optional
import json

from glam_config import create_glam_config, GLaMConfig
from glam_model import GLaMModel
from glam_moe import GLaMSparseMoE


class GLaMDemo:
    """Demo class for GLaM model testing and visualization"""
    
    def __init__(self, model_size: str = "test", device: str = "cuda" if torch.cuda.is_available() else "cpu"):
        self.device = device
        self.config = create_glam_config(model_size)
        self.model = GLaMModel(self.config).to(device)
        self.model.eval()
        
        print(f"GLaM Demo initialized with {model_size} model")
        print(f"Model parameters: {self.count_parameters():,}")
        print(f"MoE layers: {self.config.moe_layers}")
        print(f"Device: {device}")
    
    def count_parameters(self) -> int:
        """Count total model parameters"""
        return sum(p.numel() for p in self.model.parameters())
    
    def count_expert_parameters(self) -> Dict[str, int]:
        """Count parameters in expert vs non-expert components"""
        expert_params = 0
        non_expert_params = 0
        
        for name, param in self.model.named_parameters():
            if 'experts' in name:
                expert_params += param.numel()
            else:
                non_expert_params += param.numel()
        
        return {
            'expert_params': expert_params,
            'non_expert_params': non_expert_params,
            'total_params': expert_params + non_expert_params,
            'expert_ratio': expert_params / (expert_params + non_expert_params)
        }
    
    def test_forward_pass(self, batch_size: int = 2, seq_len: int = 128) -> Dict[str, Any]:
        """Test basic forward pass"""
        print(f"\n=== Testing Forward Pass ===")
        print(f"Batch size: {batch_size}, Sequence length: {seq_len}")
        
        # Create random input
        input_ids = torch.randint(0, self.config.vocab_size, (batch_size, seq_len), device=self.device)
        
        # Forward pass
        start_time = time.time()
        with torch.no_grad():
            outputs = self.model(input_ids=input_ids)
            hidden_states, _, _, _, aux_losses = outputs
        
        forward_time = time.time() - start_time
        
        results = {
            'input_shape': input_ids.shape,
            'output_shape': hidden_states.shape,
            'forward_time': forward_time,
            'aux_losses': {k: v.item() for k, v in aux_losses.items()},
            'memory_usage': torch.cuda.memory_allocated() / 1024**3 if torch.cuda.is_available() else 0
        }
        
        print(f"Input shape: {results['input_shape']}")
        print(f"Output shape: {results['output_shape']}")
        print(f"Forward time: {results['forward_time']:.4f}s")
        print(f"Auxiliary losses: {results['aux_losses']}")
        print(f"GPU memory: {results['memory_usage']:.2f} GB")
        
        return results
    
    def test_expert_routing(self, num_samples: int = 100) -> Dict[str, Any]:
        """Test expert routing patterns"""
        print(f"\n=== Testing Expert Routing ===")
        
        # Collect routing statistics
        expert_usage = {}
        routing_patterns = []
        
        for layer_idx in self.config.moe_layers:
            expert_usage[layer_idx] = torch.zeros(self.config.num_experts)
        
        self.model.eval()
        with torch.no_grad():
            for i in range(num_samples):
                # Random input
                input_ids = torch.randint(0, self.config.vocab_size, (1, 32), device=self.device)
                
                # Forward pass and collect expert usage
                outputs = self.model(input_ids=input_ids)
                
                # Get expert usage from MoE layers
                for layer_idx, layer in enumerate(self.model.layers):
                    if hasattr(layer, 'mlp') and hasattr(layer.mlp, 'get_expert_usage_stats'):
                        stats = layer.mlp.get_expert_usage_stats()
                        if layer_idx in expert_usage:
                            expert_usage[layer_idx] += stats['expert_usage_count']
        
        # Analyze routing patterns
        routing_analysis = {}
        for layer_idx, usage in expert_usage.items():
            if usage.sum() > 0:
                usage_normalized = usage / usage.sum()
                routing_analysis[layer_idx] = {
                    'usage_distribution': usage_normalized.tolist(),
                    'entropy': -(usage_normalized * torch.log(usage_normalized + 1e-8)).sum().item(),
                    'max_usage': usage_normalized.max().item(),
                    'min_usage': usage_normalized.min().item(),
                    'std_usage': usage_normalized.std().item()
                }
        
        print(f"Routing analysis for {num_samples} samples:")
        for layer_idx, analysis in routing_analysis.items():
            print(f"  Layer {layer_idx}: entropy={analysis['entropy']:.3f}, std={analysis['std_usage']:.3f}")
        
        return routing_analysis
    
    def benchmark_inference(self, sequence_lengths: List[int] = [64, 128, 256, 512]) -> Dict[str, List[float]]:
        """Benchmark inference speed across different sequence lengths"""
        print(f"\n=== Benchmarking Inference ===")
        
        results = {
            'sequence_lengths': sequence_lengths,
            'inference_times': [],
            'throughput_tokens_per_sec': [],
            'memory_usage': []
        }
        
        self.model.eval()
        
        for seq_len in sequence_lengths:
            print(f"Testing sequence length: {seq_len}")
            
            # Warmup
            input_ids = torch.randint(0, self.config.vocab_size, (1, seq_len), device=self.device)
            with torch.no_grad():
                _ = self.model(input_ids=input_ids)
            
            # Benchmark
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            start_time = time.time()
            
            num_runs = 10
            with torch.no_grad():
                for _ in range(num_runs):
                    _ = self.model(input_ids=input_ids)
            
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            end_time = time.time()
            
            avg_time = (end_time - start_time) / num_runs
            throughput = seq_len / avg_time
            memory = torch.cuda.memory_allocated() / 1024**3 if torch.cuda.is_available() else 0
            
            results['inference_times'].append(avg_time)
            results['throughput_tokens_per_sec'].append(throughput)
            results['memory_usage'].append(memory)
            
            print(f"  Time: {avg_time:.4f}s, Throughput: {throughput:.1f} tokens/s, Memory: {memory:.2f} GB")
        
        return results
    
    def compare_routing_algorithms(self) -> Dict[str, Any]:
        """Compare token choice vs expert choice routing"""
        print(f"\n=== Comparing Routing Algorithms ===")
        
        results = {}
        
        for routing_algorithm in ["token_choice", "expert_choice"]:
            print(f"\nTesting {routing_algorithm} routing...")
            
            # Create MoE layer with specific routing
            moe_layer = GLaMSparseMoE(
                hidden_size=self.config.hidden_size,
                intermediate_size=self.config.intermediate_size,
                num_experts=self.config.num_experts,
                top_k=self.config.top_k,
                routing_algorithm=routing_algorithm,
                capacity_factor=self.config.expert_capacity_factor
            ).to(self.device)
            
            # Test with random input
            batch_size, seq_len = 2, 64
            hidden_states = torch.randn(batch_size, seq_len, self.config.hidden_size, device=self.device)
            
            # Forward pass
            start_time = time.time()
            with torch.no_grad():
                output, aux_losses = moe_layer(hidden_states)
            forward_time = time.time() - start_time
            
            # Get expert usage statistics
            stats = moe_layer.get_expert_usage_stats()
            
            results[routing_algorithm] = {
                'forward_time': forward_time,
                'aux_losses': {k: v.item() for k, v in aux_losses.items()},
                'expert_usage_std': stats['usage_std'].item(),
                'expert_usage_entropy': -(stats['expert_usage_rates'] * 
                                        torch.log(stats['expert_usage_rates'] + 1e-8)).sum().item()
            }
            
            print(f"  Forward time: {forward_time:.4f}s")
            print(f"  Expert usage std: {results[routing_algorithm]['expert_usage_std']:.4f}")
            print(f"  Expert usage entropy: {results[routing_algorithm]['expert_usage_entropy']:.4f}")
        
        return results
    
    def visualize_expert_usage(self, save_path: Optional[str] = None):
        """Visualize expert usage patterns"""
        print(f"\n=== Visualizing Expert Usage ===")
        
        # Collect expert usage data
        routing_data = self.test_expert_routing(num_samples=50)
        
        if not routing_data:
            print("No MoE layers found for visualization")
            return
        
        # Create visualization
        num_layers = len(routing_data)
        fig, axes = plt.subplots(1, num_layers, figsize=(5 * num_layers, 4))
        if num_layers == 1:
            axes = [axes]
        
        for i, (layer_idx, data) in enumerate(routing_data.items()):
            usage_dist = data['usage_distribution']
            expert_indices = list(range(len(usage_dist)))
            
            axes[i].bar(expert_indices, usage_dist)
            axes[i].set_title(f'Layer {layer_idx} Expert Usage\nEntropy: {data["entropy"]:.3f}')
            axes[i].set_xlabel('Expert Index')
            axes[i].set_ylabel('Usage Probability')
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Visualization saved to {save_path}")
        else:
            plt.show()
    
    def run_comprehensive_demo(self):
        """Run comprehensive demo of all GLaM features"""
        print("=" * 60)
        print("GLaM (Generalist Language Model) Comprehensive Demo")
        print("=" * 60)
        
        # Model info
        param_info = self.count_expert_parameters()
        print(f"\nModel Information:")
        print(f"  Total parameters: {param_info['total_params']:,}")
        print(f"  Expert parameters: {param_info['expert_params']:,} ({param_info['expert_ratio']:.1%})")
        print(f"  Non-expert parameters: {param_info['non_expert_params']:,}")
        
        # Test forward pass
        forward_results = self.test_forward_pass()
        
        # Test expert routing
        routing_results = self.test_expert_routing()
        
        # Benchmark inference
        benchmark_results = self.benchmark_inference()
        
        # Compare routing algorithms
        routing_comparison = self.compare_routing_algorithms()
        
        # Visualize expert usage
        try:
            self.visualize_expert_usage("glam_expert_usage.png")
        except Exception as e:
            print(f"Visualization failed: {e}")
        
        # Summary
        print(f"\n=== Demo Summary ===")
        print(f"✓ Forward pass test completed")
        print(f"✓ Expert routing analysis completed")
        print(f"✓ Inference benchmarking completed")
        print(f"✓ Routing algorithm comparison completed")
        
        # Save results
        demo_results = {
            'model_config': {
                'model_size': 'test',
                'num_experts': self.config.num_experts,
                'num_layers': self.config.num_layers,
                'moe_layers': self.config.moe_layers
            },
            'parameter_info': param_info,
            'forward_results': forward_results,
            'routing_results': routing_results,
            'benchmark_results': benchmark_results,
            'routing_comparison': routing_comparison
        }
        
        with open('glam_demo_results.json', 'w') as f:
            json.dump(demo_results, f, indent=2)
        
        print(f"✓ Results saved to glam_demo_results.json")
        print(f"\nGLaM demo completed successfully!")


def main():
    """Main demo function"""
    # Test different model sizes
    model_sizes = ["test", "small"]
    
    for model_size in model_sizes:
        print(f"\n{'='*20} Testing {model_size.upper()} Model {'='*20}")
        
        try:
            demo = GLaMDemo(model_size=model_size)
            
            # Run basic tests
            demo.test_forward_pass(batch_size=1, seq_len=64)
            demo.test_expert_routing(num_samples=20)
            
            if model_size == "test":  # Run comprehensive demo only for test model
                demo.run_comprehensive_demo()
                
        except Exception as e:
            print(f"Error testing {model_size} model: {e}")
            continue


if __name__ == "__main__":
    main()
