"""
GLaM演示和测试
展示GLaM模型能力并提供测试工具
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import time
from typing import List, Dict, Any, Optional
import json

from glam_config import create_glam_config, GLaMConfig
from glam_model import GLaMModel
from glam_moe import GLaMSparseMoE


class GLaMDemo:
    """GLaM模型测试和可视化演示类"""

    def __init__(self, model_size: str = "test", device: str = "cuda" if torch.cuda.is_available() else "cpu"):
        self.device = device
        self.config = create_glam_config(model_size)
        self.model = GLaMModel(self.config).to(device)
        self.model.eval()

        print(f"GLaM演示已初始化，使用{model_size}模型")
        print(f"模型参数: {self.count_parameters():,}")
        print(f"MoE层: {self.config.moe_layers}")
        print(f"设备: {device}")

    def count_parameters(self) -> int:
        """计算模型总参数数"""
        return sum(p.numel() for p in self.model.parameters())

    def count_expert_parameters(self) -> Dict[str, int]:
        """计算专家与非专家组件的参数数"""
        expert_params = 0
        non_expert_params = 0

        for name, param in self.model.named_parameters():
            if 'experts' in name:
                expert_params += param.numel()
            else:
                non_expert_params += param.numel()

        return {
            'expert_params': expert_params,
            'non_expert_params': non_expert_params,
            'total_params': expert_params + non_expert_params,
            'expert_ratio': expert_params / (expert_params + non_expert_params)
        }
    
    def test_forward_pass(self, batch_size: int = 2, seq_len: int = 128) -> Dict[str, Any]:
        """测试基本前向传播"""
        print(f"\n=== 测试前向传播 ===")
        print(f"批大小: {batch_size}, 序列长度: {seq_len}")

        # 创建随机输入
        input_ids = torch.randint(0, self.config.vocab_size, (batch_size, seq_len), device=self.device)

        # 前向传播
        start_time = time.time()
        with torch.no_grad():
            outputs = self.model(input_ids=input_ids)
            hidden_states, _, _, _, aux_losses = outputs

        forward_time = time.time() - start_time

        results = {
            'input_shape': input_ids.shape,
            'output_shape': hidden_states.shape,
            'forward_time': forward_time,
            'aux_losses': {k: v.item() for k, v in aux_losses.items()},
            'memory_usage': torch.cuda.memory_allocated() / 1024**3 if torch.cuda.is_available() else 0
        }

        print(f"输入形状: {results['input_shape']}")
        print(f"输出形状: {results['output_shape']}")
        print(f"前向时间: {results['forward_time']:.4f}s")
        print(f"辅助损失: {results['aux_losses']}")
        print(f"GPU内存: {results['memory_usage']:.2f} GB")

        return results
    
    def test_expert_routing(self, num_samples: int = 100) -> Dict[str, Any]:
        """Test expert routing patterns"""
        print(f"\n=== Testing Expert Routing ===")
        
        # Collect routing statistics
        expert_usage = {}
        routing_patterns = []
        
        for layer_idx in self.config.moe_layers:
            expert_usage[layer_idx] = torch.zeros(self.config.num_experts)
        
        self.model.eval()
        with torch.no_grad():
            for i in range(num_samples):
                # Random input
                input_ids = torch.randint(0, self.config.vocab_size, (1, 32), device=self.device)
                
                # Forward pass and collect expert usage
                outputs = self.model(input_ids=input_ids)
                
                # Get expert usage from MoE layers
                for layer_idx, layer in enumerate(self.model.layers):
                    if hasattr(layer, 'mlp') and hasattr(layer.mlp, 'get_expert_usage_stats'):
                        stats = layer.mlp.get_expert_usage_stats()
                        if layer_idx in expert_usage:
                            expert_usage[layer_idx] += stats['expert_usage_count']
        
        # Analyze routing patterns
        routing_analysis = {}
        for layer_idx, usage in expert_usage.items():
            if usage.sum() > 0:
                usage_normalized = usage / usage.sum()
                routing_analysis[layer_idx] = {
                    'usage_distribution': usage_normalized.tolist(),
                    'entropy': -(usage_normalized * torch.log(usage_normalized + 1e-8)).sum().item(),
                    'max_usage': usage_normalized.max().item(),
                    'min_usage': usage_normalized.min().item(),
                    'std_usage': usage_normalized.std().item()
                }
        
        print(f"Routing analysis for {num_samples} samples:")
        for layer_idx, analysis in routing_analysis.items():
            print(f"  Layer {layer_idx}: entropy={analysis['entropy']:.3f}, std={analysis['std_usage']:.3f}")
        
        return routing_analysis
    
    def benchmark_inference(self, sequence_lengths: List[int] = [64, 128, 256, 512]) -> Dict[str, List[float]]:
        """Benchmark inference speed across different sequence lengths"""
        print(f"\n=== Benchmarking Inference ===")
        
        results = {
            'sequence_lengths': sequence_lengths,
            'inference_times': [],
            'throughput_tokens_per_sec': [],
            'memory_usage': []
        }
        
        self.model.eval()
        
        for seq_len in sequence_lengths:
            print(f"Testing sequence length: {seq_len}")
            
            # Warmup
            input_ids = torch.randint(0, self.config.vocab_size, (1, seq_len), device=self.device)
            with torch.no_grad():
                _ = self.model(input_ids=input_ids)
            
            # Benchmark
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            start_time = time.time()
            
            num_runs = 10
            with torch.no_grad():
                for _ in range(num_runs):
                    _ = self.model(input_ids=input_ids)
            
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            end_time = time.time()
            
            avg_time = (end_time - start_time) / num_runs
            throughput = seq_len / avg_time
            memory = torch.cuda.memory_allocated() / 1024**3 if torch.cuda.is_available() else 0
            
            results['inference_times'].append(avg_time)
            results['throughput_tokens_per_sec'].append(throughput)
            results['memory_usage'].append(memory)
            
            print(f"  Time: {avg_time:.4f}s, Throughput: {throughput:.1f} tokens/s, Memory: {memory:.2f} GB")
        
        return results
    
    def compare_routing_algorithms(self) -> Dict[str, Any]:
        """Compare token choice vs expert choice routing"""
        print(f"\n=== Comparing Routing Algorithms ===")
        
        results = {}
        
        for routing_algorithm in ["token_choice", "expert_choice"]:
            print(f"\nTesting {routing_algorithm} routing...")
            
            # Create MoE layer with specific routing
            moe_layer = GLaMSparseMoE(
                hidden_size=self.config.hidden_size,
                intermediate_size=self.config.intermediate_size,
                num_experts=self.config.num_experts,
                top_k=self.config.top_k,
                routing_algorithm=routing_algorithm,
                capacity_factor=self.config.expert_capacity_factor
            ).to(self.device)
            
            # Test with random input
            batch_size, seq_len = 2, 64
            hidden_states = torch.randn(batch_size, seq_len, self.config.hidden_size, device=self.device)
            
            # Forward pass
            start_time = time.time()
            with torch.no_grad():
                output, aux_losses = moe_layer(hidden_states)
            forward_time = time.time() - start_time
            
            # Get expert usage statistics
            stats = moe_layer.get_expert_usage_stats()
            
            results[routing_algorithm] = {
                'forward_time': forward_time,
                'aux_losses': {k: v.item() for k, v in aux_losses.items()},
                'expert_usage_std': stats['usage_std'].item(),
                'expert_usage_entropy': -(stats['expert_usage_rates'] * 
                                        torch.log(stats['expert_usage_rates'] + 1e-8)).sum().item()
            }
            
            print(f"  Forward time: {forward_time:.4f}s")
            print(f"  Expert usage std: {results[routing_algorithm]['expert_usage_std']:.4f}")
            print(f"  Expert usage entropy: {results[routing_algorithm]['expert_usage_entropy']:.4f}")
        
        return results
    
    def visualize_expert_usage(self, save_path: Optional[str] = None):
        """Visualize expert usage patterns"""
        print(f"\n=== Visualizing Expert Usage ===")
        
        # Collect expert usage data
        routing_data = self.test_expert_routing(num_samples=50)
        
        if not routing_data:
            print("No MoE layers found for visualization")
            return
        
        # Create visualization
        num_layers = len(routing_data)
        fig, axes = plt.subplots(1, num_layers, figsize=(5 * num_layers, 4))
        if num_layers == 1:
            axes = [axes]
        
        for i, (layer_idx, data) in enumerate(routing_data.items()):
            usage_dist = data['usage_distribution']
            expert_indices = list(range(len(usage_dist)))
            
            axes[i].bar(expert_indices, usage_dist)
            axes[i].set_title(f'Layer {layer_idx} Expert Usage\nEntropy: {data["entropy"]:.3f}')
            axes[i].set_xlabel('Expert Index')
            axes[i].set_ylabel('Usage Probability')
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Visualization saved to {save_path}")
        else:
            plt.show()
    
    def run_comprehensive_demo(self):
        """运行GLaM所有功能的综合演示"""
        print("=" * 60)
        print("GLaM (通用语言模型) 综合演示")
        print("=" * 60)

        # 模型信息
        param_info = self.count_expert_parameters()
        print(f"\n模型信息:")
        print(f"  总参数: {param_info['total_params']:,}")
        print(f"  专家参数: {param_info['expert_params']:,} ({param_info['expert_ratio']:.1%})")
        print(f"  非专家参数: {param_info['non_expert_params']:,}")

        # 测试前向传播
        forward_results = self.test_forward_pass()

        # 测试专家路由
        routing_results = self.test_expert_routing()

        # 基准测试推理
        benchmark_results = self.benchmark_inference()

        # 比较路由算法
        routing_comparison = self.compare_routing_algorithms()

        # 可视化专家使用情况
        try:
            self.visualize_expert_usage("glam_expert_usage.png")
        except Exception as e:
            print(f"可视化失败: {e}")

        # 总结
        print(f"\n=== 演示总结 ===")
        print(f"✓ 前向传播测试完成")
        print(f"✓ 专家路由分析完成")
        print(f"✓ 推理基准测试完成")
        print(f"✓ 路由算法比较完成")

        # 保存结果
        demo_results = {
            'model_config': {
                'model_size': 'test',
                'num_experts': self.config.num_experts,
                'num_layers': self.config.num_layers,
                'moe_layers': self.config.moe_layers
            },
            'parameter_info': param_info,
            'forward_results': forward_results,
            'routing_results': routing_results,
            'benchmark_results': benchmark_results,
            'routing_comparison': routing_comparison
        }

        with open('glam_demo_results.json', 'w') as f:
            json.dump(demo_results, f, indent=2)

        print(f"✓ 结果已保存到 glam_demo_results.json")
        print(f"\nGLaM演示成功完成!")


def main():
    """主演示函数"""
    # 测试不同模型大小
    model_sizes = ["test", "small"]

    for model_size in model_sizes:
        print(f"\n{'='*20} 测试 {model_size.upper()} 模型 {'='*20}")

        try:
            demo = GLaMDemo(model_size=model_size)

            # 运行基本测试
            demo.test_forward_pass(batch_size=1, seq_len=64)
            demo.test_expert_routing(num_samples=20)

            if model_size == "test":  # 仅对测试模型运行综合演示
                demo.run_comprehensive_demo()

        except Exception as e:
            print(f"测试{model_size}模型时出错: {e}")
            continue


if __name__ == "__main__":
    main()
