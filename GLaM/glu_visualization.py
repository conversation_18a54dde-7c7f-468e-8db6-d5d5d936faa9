"""
GLU门控机制可视化
直观展示GLaM专家网络中GLU机制的工作原理
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False


def visualize_glu_mechanism():
    """可视化GLU门控机制"""
    
    # 创建简单的GLU层
    hidden_size = 8
    intermediate_size = 16
    
    gate_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
    up_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
    
    # 创建测试输入
    x = torch.randn(1, 1, hidden_size)
    
    with torch.no_grad():
        gate = gate_proj(x).squeeze()
        up = up_proj(x).squeeze()
        gate_activated = F.gelu(gate)
        output = gate_activated * up
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('GLU门控机制工作原理', fontsize=16, fontweight='bold')
    
    # 1. 输入信号
    axes[0, 0].bar(range(len(x.squeeze())), x.squeeze().numpy())
    axes[0, 0].set_title('输入信号 x')
    axes[0, 0].set_xlabel('维度')
    axes[0, 0].set_ylabel('值')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 门控投影
    axes[0, 1].bar(range(len(gate)), gate.numpy(), alpha=0.7, color='orange')
    axes[0, 1].set_title('门控投影 gate_proj(x)')
    axes[0, 1].set_xlabel('中间维度')
    axes[0, 1].set_ylabel('值')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 上投影
    axes[0, 2].bar(range(len(up)), up.numpy(), alpha=0.7, color='green')
    axes[0, 2].set_title('上投影 up_proj(x)')
    axes[0, 2].set_xlabel('中间维度')
    axes[0, 2].set_ylabel('值')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 激活后的门控
    axes[1, 0].bar(range(len(gate_activated)), gate_activated.numpy(), alpha=0.7, color='red')
    axes[1, 0].set_title('激活门控 GELU(gate)')
    axes[1, 0].set_xlabel('中间维度')
    axes[1, 0].set_ylabel('值')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 门控效果对比
    x_pos = np.arange(len(up))
    width = 0.35
    axes[1, 1].bar(x_pos - width/2, up.numpy(), width, label='up_proj(x)', alpha=0.7)
    axes[1, 1].bar(x_pos + width/2, output.numpy(), width, label='gate * up', alpha=0.7)
    axes[1, 1].set_title('门控效果对比')
    axes[1, 1].set_xlabel('中间维度')
    axes[1, 1].set_ylabel('值')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 门控选择性
    selectivity = gate_activated / (gate_activated.max() + 1e-8)
    axes[1, 2].bar(range(len(selectivity)), selectivity.numpy(), alpha=0.7, color='purple')
    axes[1, 2].set_title('门控选择性 (归一化)')
    axes[1, 2].set_xlabel('中间维度')
    axes[1, 2].set_ylabel('选择强度')
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('GLaM/glu_mechanism.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return gate, up, gate_activated, output


def compare_activation_functions():
    """比较不同激活函数在门控中的效果"""
    
    x = torch.linspace(-3, 3, 100)
    
    # 不同激活函数
    activations = {
        'ReLU': F.relu(x),
        'GELU': F.gelu(x),
        'Swish/SiLU': F.silu(x),
        'Tanh': torch.tanh(x)
    }
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('不同激活函数在门控中的表现', fontsize=16, fontweight='bold')
    
    axes = axes.flatten()
    
    for i, (name, y) in enumerate(activations.items()):
        axes[i].plot(x.numpy(), y.numpy(), linewidth=2, label=name)
        axes[i].set_title(f'{name} 激活函数')
        axes[i].set_xlabel('输入值')
        axes[i].set_ylabel('输出值')
        axes[i].grid(True, alpha=0.3)
        axes[i].legend()
        
        # 添加特性说明
        if name == 'ReLU':
            axes[i].text(0.5, 0.8, '硬截断\n梯度消失', transform=axes[i].transAxes, 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.5))
        elif name == 'GELU':
            axes[i].text(0.5, 0.8, '平滑过渡\n概率解释', transform=axes[i].transAxes,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.5))
        elif name == 'Swish/SiLU':
            axes[i].text(0.5, 0.8, '自门控\n无上界', transform=axes[i].transAxes,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))
        else:  # Tanh
            axes[i].text(0.5, 0.8, '有界输出\n梯度饱和', transform=axes[i].transAxes,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.5))
    
    plt.tight_layout()
    plt.savefig('GLaM/activation_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


def analyze_expert_specialization():
    """分析专家特化程度"""
    
    hidden_size = 64
    intermediate_size = 256
    num_experts = 8
    num_samples = 100
    
    # 创建多个专家
    experts = []
    for i in range(num_experts):
        expert = nn.ModuleDict({
            'gate_proj': nn.Linear(hidden_size, intermediate_size, bias=False),
            'up_proj': nn.Linear(hidden_size, intermediate_size, bias=False)
        })
        experts.append(expert)
    
    # 生成不同类型的输入
    input_types = {
        '随机输入': torch.randn(num_samples, hidden_size),
        '正值输入': torch.abs(torch.randn(num_samples, hidden_size)),
        '稀疏输入': torch.randn(num_samples, hidden_size) * (torch.rand(num_samples, hidden_size) > 0.8),
        '周期输入': torch.sin(torch.arange(num_samples).unsqueeze(1) * torch.linspace(0, 2*np.pi, hidden_size))
    }
    
    # 分析每个专家对不同输入的响应
    expert_responses = np.zeros((num_experts, len(input_types)))
    
    with torch.no_grad():
        for expert_idx, expert in enumerate(experts):
            for input_idx, (input_name, inputs) in enumerate(input_types.items()):
                responses = []
                for x in inputs:
                    gate = expert['gate_proj'](x.unsqueeze(0))
                    up = expert['up_proj'](x.unsqueeze(0))
                    gate_activated = F.gelu(gate)
                    output = gate_activated * up
                    
                    # 计算响应强度 (输出的L2范数)
                    response_strength = output.norm().item()
                    responses.append(response_strength)
                
                expert_responses[expert_idx, input_idx] = np.mean(responses)
    
    # 可视化专家特化
    fig, ax = plt.subplots(figsize=(10, 8))
    
    im = ax.imshow(expert_responses, cmap='viridis', aspect='auto')
    
    # 设置标签
    ax.set_xticks(range(len(input_types)))
    ax.set_xticklabels(list(input_types.keys()))
    ax.set_yticks(range(num_experts))
    ax.set_yticklabels([f'专家 {i+1}' for i in range(num_experts)])
    
    # 添加数值标注
    for i in range(num_experts):
        for j in range(len(input_types)):
            text = ax.text(j, i, f'{expert_responses[i, j]:.2f}',
                          ha="center", va="center", color="white", fontweight='bold')
    
    ax.set_title('专家对不同输入类型的响应强度', fontsize=14, fontweight='bold')
    ax.set_xlabel('输入类型')
    ax.set_ylabel('专家编号')
    
    # 添加颜色条
    cbar = plt.colorbar(im)
    cbar.set_label('响应强度', rotation=270, labelpad=15)
    
    plt.tight_layout()
    plt.savefig('GLaM/expert_specialization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return expert_responses


def main():
    """主函数"""
    
    print("=== GLU门控机制深度分析 ===\n")
    
    print("1. 可视化GLU门控机制...")
    gate, up, gate_activated, output = visualize_glu_mechanism()
    
    print(f"   门控信号范围: [{gate.min():.3f}, {gate.max():.3f}]")
    print(f"   激活后范围: [{gate_activated.min():.3f}, {gate_activated.max():.3f}]")
    print(f"   信息保留率: {(gate_activated > 0.1).float().mean():.3f}")
    
    print("\n2. 比较不同激活函数...")
    compare_activation_functions()
    
    print("\n3. 分析专家特化程度...")
    expert_responses = analyze_expert_specialization()
    
    # 计算专家特化指标
    specialization_scores = np.std(expert_responses, axis=1)
    print(f"   专家特化程度 (标准差): {specialization_scores}")
    print(f"   平均特化程度: {specialization_scores.mean():.3f}")
    print(f"   最特化专家: 专家 {specialization_scores.argmax() + 1}")
    
    print("\n=== 总结 ===")
    print("GLaM专家网络的GLU设计优势:")
    print("✓ 门控机制提供选择性信息传递")
    print("✓ GELU激活函数提供平滑的梯度流")
    print("✓ 专家能够学习不同的特化模式")
    print("✓ 相比传统FFN，表达能力显著增强")


if __name__ == "__main__":
    main()
