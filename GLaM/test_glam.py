"""
GLaM模型测试脚本
验证所有组件是否正常工作
"""

import torch
import sys
import traceback

def test_config():
    """测试配置模块"""
    print("测试配置模块...")
    try:
        from glam_config import create_glam_config, GLaMTrainingConfig
        
        # 测试模型配置
        config = create_glam_config("test")
        print(f"✓ 测试配置创建成功: {config.hidden_size}维, {config.num_experts}个专家")
        
        # 测试训练配置
        train_config = GLaMTrainingConfig()
        print(f"✓ 训练配置创建成功: 学习率={train_config.learning_rate}")
        
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_routing():
    """测试路由模块"""
    print("\n测试路由模块...")
    try:
        from glam_routing import TokenChoiceRouter, ExpertChoiceRouter, create_router
        
        hidden_size = 512
        num_experts = 8
        batch_size = 2
        seq_len = 10
        
        # 测试token选择路由
        router = TokenChoiceRouter(hidden_size, num_experts, top_k=2)
        hidden_states = torch.randn(batch_size, seq_len, hidden_size)
        expert_weights, expert_indices, router_logits = router(hidden_states)
        print(f"✓ Token选择路由测试成功: 权重形状={expert_weights.shape}")
        
        # 测试专家选择路由
        ec_router = ExpertChoiceRouter(hidden_size, num_experts)
        expert_weights_ec, expert_mask_ec, router_logits_ec = ec_router(hidden_states)
        print(f"✓ 专家选择路由测试成功: 权重形状={expert_weights_ec.shape}")
        
        # 测试工厂函数
        router_factory = create_router("token_choice", hidden_size, num_experts)
        print(f"✓ 路由器工厂函数测试成功")
        
        return True
    except Exception as e:
        print(f"✗ 路由模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_moe():
    """测试MoE模块"""
    print("\n测试MoE模块...")
    try:
        from glam_moe import GLaMExpert, GLaMSparseMoE
        
        hidden_size = 512
        intermediate_size = 2048
        num_experts = 8
        batch_size = 2
        seq_len = 10
        
        # 测试单个专家
        expert = GLaMExpert(hidden_size, intermediate_size)
        x = torch.randn(batch_size, seq_len, hidden_size)
        expert_output = expert(x)
        print(f"✓ 专家网络测试成功: 输出形状={expert_output.shape}")
        
        # 测试稀疏MoE
        moe = GLaMSparseMoE(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            num_experts=num_experts,
            top_k=2,
            routing_algorithm="token_choice"
        )
        
        output, aux_losses = moe(x)
        print(f"✓ 稀疏MoE测试成功: 输出形状={output.shape}, 辅助损失={list(aux_losses.keys())}")
        
        return True
    except Exception as e:
        print(f"✗ MoE模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_model():
    """测试完整模型"""
    print("\n测试完整模型...")
    try:
        from glam_config import create_glam_config
        from glam_model import GLaMModel
        
        # 创建小型测试模型
        config = create_glam_config("test")
        model = GLaMModel(config)
        
        # 测试前向传播
        batch_size = 2
        seq_len = 32
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        
        with torch.no_grad():
            outputs = model(input_ids=input_ids)
            hidden_states, _, _, _, aux_losses = outputs
        
        print(f"✓ 完整模型测试成功:")
        print(f"  输入形状: {input_ids.shape}")
        print(f"  输出形状: {hidden_states.shape}")
        print(f"  辅助损失: {list(aux_losses.keys())}")
        print(f"  模型参数: {sum(p.numel() for p in model.parameters()):,}")
        
        return True
    except Exception as e:
        print(f"✗ 完整模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_demo():
    """测试演示模块"""
    print("\n测试演示模块...")
    try:
        from glam_demo import GLaMDemo
        
        # 创建演示实例
        demo = GLaMDemo("test", device="cpu")  # 使用CPU避免GPU依赖
        
        # 测试参数计数
        param_info = demo.count_expert_parameters()
        print(f"✓ 演示模块测试成功:")
        print(f"  总参数: {param_info['total_params']:,}")
        print(f"  专家参数比例: {param_info['expert_ratio']:.1%}")
        
        # 测试前向传播
        results = demo.test_forward_pass(batch_size=1, seq_len=32)
        print(f"  前向传播时间: {results['forward_time']:.4f}s")
        
        return True
    except Exception as e:
        print(f"✗ 演示模块测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("GLaM模型组件测试")
    print("=" * 60)
    
    tests = [
        ("配置模块", test_config),
        ("路由模块", test_routing),
        ("MoE模块", test_moe),
        ("完整模型", test_model),
        ("演示模块", test_demo),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! GLaM实现工作正常。")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
