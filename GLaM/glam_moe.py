"""
GLaM Mixture of Experts Implementation
Based on the GLaM paper architecture with efficient sparse routing
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, Any
import math

from glam_routing import create_router, LoadBalancingLoss, RouterZLoss


class GLaMExpert(nn.Module):
    """
    Individual expert network - a simple FFN
    """
    
    def __init__(self, hidden_size: int, intermediate_size: int, dropout: float = 0.1):
        super().__init__()
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        
        # Two-layer FFN with GELU activation
        self.gate_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
        self.up_proj = nn.Linear(hidden_size, intermediate_size, bias=False)
        self.down_proj = nn.Linear(intermediate_size, hidden_size, bias=False)
        self.dropout = nn.Dropout(dropout)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize expert weights"""
        nn.init.normal_(self.gate_proj.weight, mean=0.0, std=0.02)
        nn.init.normal_(self.up_proj.weight, mean=0.0, std=0.02)
        nn.init.normal_(self.down_proj.weight, mean=0.0, std=0.02)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [batch_size, seq_len, hidden_size]
        
        Returns:
            output: [batch_size, seq_len, hidden_size]
        """
        # GLU-style gating: gate_proj(x) * GELU(up_proj(x))
        gate = self.gate_proj(x)
        up = self.up_proj(x)
        intermediate = F.gelu(gate) * up
        intermediate = self.dropout(intermediate)
        output = self.down_proj(intermediate)
        return output


class GLaMSparseMoE(nn.Module):
    """
    GLaM Sparse Mixture of Experts Layer
    Implements both token choice and expert choice routing
    """
    
    def __init__(
        self,
        hidden_size: int,
        intermediate_size: int,
        num_experts: int,
        top_k: int = 2,
        capacity_factor: float = 1.25,
        routing_algorithm: str = "token_choice",
        load_balancing_weight: float = 0.01,
        router_z_loss_weight: float = 0.001,
        dropout: float = 0.1
    ):
        super().__init__()
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.num_experts = num_experts
        self.top_k = top_k
        self.capacity_factor = capacity_factor
        self.routing_algorithm = routing_algorithm
        self.load_balancing_weight = load_balancing_weight
        self.router_z_loss_weight = router_z_loss_weight
        
        # Create experts
        self.experts = nn.ModuleList([
            GLaMExpert(hidden_size, intermediate_size, dropout)
            for _ in range(num_experts)
        ])
        
        # Create router
        self.router = create_router(
            routing_algorithm=routing_algorithm,
            hidden_size=hidden_size,
            num_experts=num_experts,
            top_k=top_k,
            capacity_factor=capacity_factor
        )
        
        # Loss functions
        self.load_balancing_loss = LoadBalancingLoss(num_experts, top_k)
        self.router_z_loss = RouterZLoss()
        
        # Statistics tracking
        self.register_buffer('expert_usage_count', torch.zeros(num_experts))
        self.register_buffer('total_tokens', torch.tensor(0.0))
    
    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
        
        Returns:
            output: [batch_size, seq_len, hidden_size]
            aux_losses: Dictionary of auxiliary losses
        """
        batch_size, seq_len, hidden_size = hidden_states.shape
        original_shape = hidden_states.shape
        
        # Flatten input for easier processing
        hidden_flat = hidden_states.view(-1, hidden_size)  # [batch_size * seq_len, hidden_size]
        
        # Route tokens to experts
        if self.routing_algorithm == "token_choice":
            expert_weights, expert_indices, router_logits = self.router(hidden_states)
            output = self._token_choice_forward(hidden_flat, expert_weights, expert_indices)
        else:  # expert_choice
            expert_weights, expert_mask, router_logits = self.router(hidden_states)
            output = self._expert_choice_forward(hidden_flat, expert_weights, expert_mask)
        
        # Reshape output
        output = output.view(original_shape)
        
        # Compute auxiliary losses
        aux_losses = self._compute_aux_losses(router_logits, expert_weights, expert_indices if self.routing_algorithm == "token_choice" else None)
        
        # Update statistics
        if self.training:
            self._update_statistics(expert_indices if self.routing_algorithm == "token_choice" else expert_mask, batch_size * seq_len)
        
        return output, aux_losses
    
    def _token_choice_forward(
        self, 
        hidden_flat: torch.Tensor, 
        expert_weights: torch.Tensor, 
        expert_indices: torch.Tensor
    ) -> torch.Tensor:
        """Forward pass for token choice routing"""
        batch_size, seq_len, top_k = expert_weights.shape
        total_tokens = batch_size * seq_len
        
        # Flatten routing information
        expert_weights_flat = expert_weights.view(total_tokens, top_k)
        expert_indices_flat = expert_indices.view(total_tokens, top_k)
        
        # Initialize output
        output = torch.zeros_like(hidden_flat)
        
        # Process each token
        for token_idx in range(total_tokens):
            token_output = torch.zeros_like(hidden_flat[token_idx])
            
            for k in range(top_k):
                expert_idx = expert_indices_flat[token_idx, k].item()
                weight = expert_weights_flat[token_idx, k]
                
                # Get expert output
                expert_input = hidden_flat[token_idx:token_idx+1]  # [1, hidden_size]
                expert_output = self.experts[expert_idx](expert_input.unsqueeze(0)).squeeze(0)  # [1, hidden_size]
                
                # Accumulate weighted output
                token_output += weight * expert_output.squeeze(0)
            
            output[token_idx] = token_output
        
        return output
    
    def _expert_choice_forward(
        self, 
        hidden_flat: torch.Tensor, 
        expert_weights: torch.Tensor, 
        expert_mask: torch.Tensor
    ) -> torch.Tensor:
        """Forward pass for expert choice routing"""
        batch_size, seq_len, num_experts = expert_weights.shape
        total_tokens = batch_size * seq_len
        
        # Flatten routing information
        expert_weights_flat = expert_weights.view(total_tokens, num_experts)
        expert_mask_flat = expert_mask.view(total_tokens, num_experts)
        
        # Initialize output
        output = torch.zeros_like(hidden_flat)
        
        # Process each expert
        for expert_idx in range(num_experts):
            # Find tokens assigned to this expert
            expert_tokens_mask = expert_mask_flat[:, expert_idx] > 0
            if not expert_tokens_mask.any():
                continue
            
            # Get tokens for this expert
            expert_tokens = hidden_flat[expert_tokens_mask]  # [num_tokens_for_expert, hidden_size]
            expert_weights_for_tokens = expert_weights_flat[expert_tokens_mask, expert_idx]
            
            if expert_tokens.size(0) > 0:
                # Process tokens through expert
                expert_output = self.experts[expert_idx](expert_tokens.unsqueeze(0)).squeeze(0)  # [num_tokens_for_expert, hidden_size]
                
                # Apply weights and accumulate
                weighted_output = expert_output * expert_weights_for_tokens.unsqueeze(-1)
                output[expert_tokens_mask] += weighted_output
        
        return output
    
    def _compute_aux_losses(
        self, 
        router_logits: torch.Tensor, 
        expert_weights: torch.Tensor,
        expert_indices: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """Compute auxiliary losses for training"""
        aux_losses = {}
        
        # Router Z-loss
        z_loss = self.router_z_loss(router_logits)
        aux_losses['router_z_loss'] = self.router_z_loss_weight * z_loss
        
        # Load balancing loss
        if self.routing_algorithm == "token_choice" and expert_indices is not None:
            router_probs = F.softmax(router_logits, dim=-1)
            lb_loss = self.load_balancing_loss(router_probs, expert_indices)
            aux_losses['load_balancing_loss'] = self.load_balancing_weight * lb_loss
        
        return aux_losses
    
    def _update_statistics(self, routing_info: torch.Tensor, num_tokens: int):
        """Update expert usage statistics"""
        if self.routing_algorithm == "token_choice":
            # routing_info is expert_indices: [batch_size, seq_len, top_k]
            expert_indices_flat = routing_info.view(-1)
            for expert_idx in range(self.num_experts):
                count = (expert_indices_flat == expert_idx).sum().float()
                self.expert_usage_count[expert_idx] += count
        else:
            # routing_info is expert_mask: [batch_size, seq_len, num_experts]
            expert_usage = routing_info.sum(dim=[0, 1])  # [num_experts]
            self.expert_usage_count += expert_usage
        
        self.total_tokens += num_tokens
    
    def get_expert_usage_stats(self) -> Dict[str, torch.Tensor]:
        """Get expert usage statistics"""
        if self.total_tokens > 0:
            usage_rates = self.expert_usage_count / self.total_tokens
            return {
                'expert_usage_count': self.expert_usage_count.clone(),
                'expert_usage_rates': usage_rates,
                'total_tokens': self.total_tokens.clone(),
                'usage_std': usage_rates.std(),
                'usage_max': usage_rates.max(),
                'usage_min': usage_rates.min()
            }
        else:
            return {
                'expert_usage_count': self.expert_usage_count.clone(),
                'expert_usage_rates': torch.zeros_like(self.expert_usage_count),
                'total_tokens': self.total_tokens.clone(),
                'usage_std': torch.tensor(0.0),
                'usage_max': torch.tensor(0.0),
                'usage_min': torch.tensor(0.0)
            }
    
    def reset_statistics(self):
        """Reset expert usage statistics"""
        self.expert_usage_count.zero_()
        self.total_tokens.zero_()


# Example usage and testing
if __name__ == "__main__":
    print("Testing GLaM Sparse MoE...")
    
    # Model parameters
    hidden_size = 512
    intermediate_size = 2048
    num_experts = 8
    top_k = 2
    batch_size = 2
    seq_len = 10
    
    # Test token choice routing
    print("\n1. Testing Token Choice Routing...")
    moe_tc = GLaMSparseMoE(
        hidden_size=hidden_size,
        intermediate_size=intermediate_size,
        num_experts=num_experts,
        top_k=top_k,
        routing_algorithm="token_choice"
    )
    
    hidden_states = torch.randn(batch_size, seq_len, hidden_size)
    output_tc, aux_losses_tc = moe_tc(hidden_states)
    
    print(f"Input shape: {hidden_states.shape}")
    print(f"Output shape: {output_tc.shape}")
    print(f"Auxiliary losses: {list(aux_losses_tc.keys())}")
    
    # Test expert choice routing
    print("\n2. Testing Expert Choice Routing...")
    moe_ec = GLaMSparseMoE(
        hidden_size=hidden_size,
        intermediate_size=intermediate_size,
        num_experts=num_experts,
        routing_algorithm="expert_choice",
        capacity_factor=1.5
    )
    
    output_ec, aux_losses_ec = moe_ec(hidden_states)
    print(f"Expert choice output shape: {output_ec.shape}")
    print(f"Expert choice aux losses: {list(aux_losses_ec.keys())}")
    
    # Test statistics
    print("\n3. Testing Statistics...")
    stats = moe_tc.get_expert_usage_stats()
    print(f"Expert usage stats: {stats}")
    
    print("\nGLaM Sparse MoE testing completed!")
