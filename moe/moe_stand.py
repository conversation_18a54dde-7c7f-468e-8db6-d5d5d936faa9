import time

import torch
import torch.nn as nn
import torch.nn.functional as F



class Expert(nn.Module):

    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.1):
        super(Expert, self).__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim)
        )

        # self.fc1 = nn.Linear(input_dim, hidden_dim)
        # self.act = nn.GELU()
        # self.dropout = nn.Dropout(dropout)
        # self.fc2 = nn.Linear(hidden_dim, output_dim)


    def forward(self, x):
        return self.net(x)

class MoE(nn.Module):

    def __init__(self, input_dim, hidden_dim, output_dim, num_experts, top_k, expert_capacity=1.25):

        super(MoE, self).__init__()

        self.top_k = top_k
        self.expert_capacity = expert_capacity
        self.num_experts = num_experts

        # 门控网络
        self.gate = nn.Linear(input_dim, num_experts)

        self.experts = nn.ModuleList([
            Expert(input_dim, hidden_dim, output_dim) for _ in range(num_experts)
        ])


    def forward(self, x):
        # x 是输入张量（shape 通常是 [batch_size, in_features]）
        batch_size, input_dim = x.shape
        device = x.device

        # 门控计算, 路由选择，选择专家
        logits = self.gate(x)
        probs = torch.softmax(logits, dim=-1)

        # 获取概率大的前K个专家，分别获取他们的概率和索引
        topk_probs, topk_indices = torch.topk(probs, self.top_k, dim=-1)


        # 实现重要性损失，辅助损失计算
        if self.training:
            # 重要性损失（专家利用率均衡）
            importance = probs.sum(0)
            importance_loss = torch.var(importance) / (self.num_experts ** 2)

            # 负载均衡损失
            mask = torch.zeros_like(probs, dtype=torch.bool)
            mask.scatter_(1, topk_indices, True)
            routing_probs = probs * mask
            expert_usage = mask.float().mean(0)
            routing_weight = routing_probs.mean(0)

            load_balance_loss = self.num_experts * (expert_usage * routing_weight).sum()

            aux_loss = importance_loss + load_balance_loss
        else:
            aux_loss = 0.0

        # 将索引展平
        topk_probs.flatten()
        flat_probs = topk_probs.view(-1)
        flat_indices = topk_indices.view(-1)
        # 创建一个[0, batch_size-1]的索引，[:, None]通过添加一个新的维度，转为[batch_size, 1]的张量
        sample_indices = torch.arange(batch_size, device=device)[:, None]
        # sample_indices.expand(-1, self.top_k)将sample_indices转为[batch_size, top_k] 的张量，.flatten()转为[batch_size * top_k]一维
        sample_indices = sample_indices.expand(-1, self.top_k).flatten()

        # 初始化输出
        outputs = torch.zeros(batch_size, self.experts[0].net[-1].out_features, device=device)

        # 处理每个专家
        for expert_idx in range(self.num_experts):
            # 是否为挑选出来的专家,形状为tensor([False, False,  True,  True, False, False])
            expert_mask = (flat_indices == expert_idx)
            # 根据expert_mask张量中为True的元素，从sample_indices中取出对应的元素和从flat_probs中取出对应的概率
            expert_samples = sample_indices[expert_mask]
            expert_weights = flat_probs[expert_mask]

            # 控制给予专家的样本（TOKEN）数量
            if len(expert_samples) > self.expert_capacity:
                expert_weights = expert_weights[:self.expert_capacity]
                expert_samples = expert_samples[:self.expert_capacity]

            if len(expert_samples) == 0:
                continue

            # 获取专家输入
            expert_input = x[expert_samples]
            exper_output = self.experts[expert_idx](expert_input)
            weighted_output = exper_output * expert_weights.unsqueeze(-1)

            # 累加输出
            outputs.index_add_(0, expert_samples, weighted_output)

            print(outputs.shape)
            print('outputs.shape: ', outputs)


            return outputs, aux_loss


if __name__ == "__main__":
    input_dim = 128
    output_dim = 256
    num_experts = 8
    top_k = 2
    expert_capacity = 32
    hidden_dim = 512
    batch_size = 64

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    moe = MoE(input_dim, hidden_dim, output_dim, num_experts, top_k, expert_capacity).to(device)
    x = torch.randn(batch_size, input_dim).to(device)


    for _ in range(10):
        moe.train()
        output, loss = moe(x)
        print(f"Using device: {x.device}")
        print(f'Training output shape: {output.shape}')
        print(f"Training auxiliary loss: {loss.item():.4f}")

    # 推理模式
    moe.eval()
    output, _ = moe(x)
    print(f'Eval output shape: {output.shape}')




# flat_indices = torch.tensor([2, 1, 0, 0, 1, 2])
# sample_indices = torch.tensor([0, 0, 0, 1, 1, 1])
# flat_probs = torch.tensor([0.9, 0.8, 0.7, 0.6, 0.5, 0.4])
# expert_mask = (flat_indices == 0)  # tensor([False, False, True, True, False, False])
# expert_weights = sample_indices[expert_mask]  # tensor([0, 1])
# expert_samples = flat_probs[expert_mask]  # tensor([0.7, 0.6])
#
#
# print(expert_mask)


