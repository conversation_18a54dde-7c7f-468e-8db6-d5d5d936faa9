#!/usr/bin/env python3
"""
动态MoE快速开始指南
只需要这一个文件就能运行基本的动态MoE模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from improved_dynamic_moe import ImprovedDynamicMoETransformer, SimplePPOTrainer

def quick_demo():
    """快速演示动态MoE的基本功能"""
    print("🚀 动态MoE快速演示")
    print("=" * 50)
    
    # 1. 创建模型（小型配置，适合快速测试）
    print("📦 创建模型...")
    model = ImprovedDynamicMoETransformer(
        vocab_size=1000,       # 小词汇表
        embed_dim=256,         # 较小的嵌入维度
        num_heads=4,           # 4个注意力头
        group_size=2,          # GQA分组大小
        window_size=128,       # 小窗口大小
        num_experts=8,         # 8个专家
        top_k=2,              # 激活2个专家
        num_layers=2          # 2层
    )
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"✅ 模型创建成功！参数数量: {total_params:,}")
    
    # 2. 测试前向传播
    print("\n🔄 测试前向传播...")
    batch_size = 2
    seq_len = 32
    test_input = torch.randint(0, 1000, (batch_size, seq_len))
    
    model.eval()
    with torch.no_grad():
        output, load_loss = model(test_input)
    
    print(f"✅ 前向传播成功！")
    print(f"   输入形状: {test_input.shape}")
    print(f"   输出形状: {output.shape}")
    print(f"   负载均衡损失: {load_loss:.4f}")
    
    # 3. 分析专家使用情况
    print("\n📊 专家使用分析...")
    for layer_idx, layer in enumerate(model.layers):
        if hasattr(layer, 'moe'):
            expert_counts = layer.moe.expert_counts
            active_experts = (expert_counts > 0).sum().item()
            print(f"   Layer {layer_idx}: 激活了 {active_experts}/{layer.moe.num_experts} 个专家")
    
    return model

def simple_training():
    """简单的训练示例"""
    print("\n🎯 简单训练示例")
    print("=" * 50)
    
    # 创建模型
    model = ImprovedDynamicMoETransformer(
        vocab_size=500, embed_dim=128, num_heads=4, group_size=2,
        window_size=64, num_experts=4, top_k=2, num_layers=1
    )
    
    # 创建简单数据
    batch_size = 4
    seq_len = 16
    num_batches = 10
    
    print(f"📚 创建训练数据: {num_batches} 批次，每批 {batch_size} 样本")
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss(ignore_index=0)
    optimizer = optim.Adam(model.parameters(), lr=1e-3)
    ppo_trainer = SimplePPOTrainer(model, lr=1e-3)
    
    # 训练循环
    model.train()
    print("\n🏃 开始训练...")
    
    for batch_idx in range(num_batches):
        # 生成随机数据
        inputs = torch.randint(1, 500, (batch_size, seq_len))
        targets = torch.randint(1, 500, (batch_size, seq_len))
        
        # 前向传播
        optimizer.zero_grad()
        outputs, load_loss = model(inputs)
        
        # 计算损失
        main_loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
        total_loss = main_loss + 0.01 * load_loss
        
        # 反向传播
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # PPO更新（简化版）
        with torch.no_grad():  # 避免梯度计算错误
            accuracy = compute_simple_accuracy(targets, outputs)
            reward = accuracy - 0.001 * load_loss.item()
        ppo_trainer.update(reward)
        
        if batch_idx % 3 == 0:
            print(f"   Batch {batch_idx+1}/{num_batches}: "
                  f"Loss={total_loss.item():.4f}, "
                  f"Acc={accuracy:.3f}, "
                  f"Reward={reward:.3f}")
    
    print("✅ 训练完成！")
    return model

def compute_simple_accuracy(targets, outputs):
    """计算简单准确率"""
    _, predicted = torch.max(outputs, dim=-1)
    mask = (targets != 0).float()
    correct = (predicted == targets).float() * mask
    accuracy = correct.sum() / mask.sum() if mask.sum() > 0 else torch.tensor(0.0)
    return accuracy.item()

def compare_configurations():
    """比较不同配置的性能"""
    print("\n⚖️  配置性能比较")
    print("=" * 50)
    
    configs = [
        ("小型模型", {"num_experts": 4, "top_k": 2, "embed_dim": 128}),
        ("中型模型", {"num_experts": 8, "top_k": 4, "embed_dim": 256}),
        ("大型模型", {"num_experts": 16, "top_k": 4, "embed_dim": 512}),
    ]
    
    test_input = torch.randint(0, 1000, (2, 64))
    
    print(f"{'配置':<10} {'参数量':<12} {'推理时间(ms)':<15} {'内存(MB)':<12}")
    print("-" * 55)
    
    for name, config in configs:
        try:
            model = ImprovedDynamicMoETransformer(
                vocab_size=1000,
                embed_dim=config["embed_dim"],
                num_heads=4,
                group_size=2,
                window_size=128,
                num_experts=config["num_experts"],
                top_k=config["top_k"],
                num_layers=2
            )
            
            # 计算参数量
            params = sum(p.numel() for p in model.parameters())
            
            # 测试推理时间
            model.eval()
            import time
            start_time = time.time()
            with torch.no_grad():
                for _ in range(5):
                    output, _ = model(test_input)
            avg_time = (time.time() - start_time) / 5 * 1000
            
            # 估算内存使用
            memory_mb = params * 4 / (1024 * 1024)  # 假设FP32
            
            print(f"{name:<10} {params:>10,} {avg_time:>12.2f} {memory_mb:>10.1f}")
            
        except Exception as e:
            print(f"{name:<10} {'错误':<12} {str(e)[:20]:<15}")

def main():
    """主函数"""
    print("🎉 动态MoE快速开始指南")
    print("这个脚本展示了如何使用改进的动态MoE模型")
    print()
    
    try:
        # 1. 基本演示
        model = quick_demo()
        
        # 2. 简单训练
        trained_model = simple_training()
        
        # 3. 配置比较
        compare_configurations()
        
        print("\n" + "=" * 50)
        print("🎊 所有演示完成！")
        print("\n📋 下一步建议:")
        print("1. 运行 'python train_dynamic_moe.py' 进行完整训练")
        print("2. 运行 'python demo_dynamic_moe.py' 查看详细功能演示")
        print("3. 在自己的项目中导入 ImprovedDynamicMoETransformer")
        
        print("\n💡 核心文件:")
        print("- improved_dynamic_moe.py (模型定义)")
        print("- train_dynamic_moe.py (训练脚本)")
        print("- demo_dynamic_moe.py (功能演示)")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
