import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import time
from improved_dynamic_moe import ImprovedDynamicMoETransformer, SimplePPOTrainer

# 简单数据集类
class SimpleTextDataset(Dataset):
    def __init__(self, data, labels, seq_len=128):
        self.data = data
        self.labels = labels
        self.seq_len = seq_len

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        # 确保序列长度一致
        data_item = self.data[idx][:self.seq_len]
        label_item = self.labels[idx][:self.seq_len]
        
        # 填充到固定长度
        if len(data_item) < self.seq_len:
            data_item = np.pad(data_item, (0, self.seq_len - len(data_item)), 'constant')
            label_item = np.pad(label_item, (0, self.seq_len - len(label_item)), 'constant')
        
        return torch.tensor(data_item, dtype=torch.long), torch.tensor(label_item, dtype=torch.long)

# 性能评估工具
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'accuracy': [],
            'loss': [],
            'reward': [],
            'flops_efficiency': [],
            'expert_utilization': []
        }
        
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if key in self.metrics:
                self.metrics[key].append(value)
    
    def get_summary(self, last_n=50):
        summary = {}
        for key, values in self.metrics.items():
            if values:
                recent_values = values[-last_n:]
                summary[f'{key}_mean'] = np.mean(recent_values)
                summary[f'{key}_std'] = np.std(recent_values)
        return summary
    
    def print_summary(self, last_n=50):
        summary = self.get_summary(last_n)
        print(f"\n=== 性能摘要 (最近{last_n}步) ===")
        for key, value in summary.items():
            print(f"{key}: {value:.4f}")
        print("=" * 40)

# 计算任务准确率
def compute_accuracy(y_true, y_pred):
    """计算准确率，忽略padding token (0)"""
    if y_pred.dim() == 3:
        _, predicted = torch.max(y_pred, dim=-1)
    else:
        predicted = y_pred
    
    mask = (y_true != 0).float()
    correct = (predicted == y_true).float() * mask
    accuracy = correct.sum() / mask.sum() if mask.sum() > 0 else torch.tensor(0.0)
    return accuracy

# 估算FLOPs
def estimate_flops(model, input_shape, num_active_experts=4):
    """估算模型FLOPs"""
    batch_size, seq_len = input_shape
    embed_dim = model.embedding.embedding_dim
    
    # 注意力FLOPs: 4 * batch_size * seq_len^2 * embed_dim
    attention_flops = 4 * batch_size * seq_len * seq_len * embed_dim
    
    # MoE FLOPs: 只计算激活的专家
    moe_flops = 8 * batch_size * seq_len * embed_dim * embed_dim * num_active_experts / 64
    
    total_flops = (attention_flops + moe_flops) * len(model.layers)
    return total_flops

# 动态MoE训练函数
def train_dynamic_moe_step(model, ppo_trainer, batch, criterion, lambda_flops=0.01, lambda_balance=0.01):
    """单步动态MoE训练"""
    inputs, targets = batch

    # 前向传播（在no_grad下计算奖励相关指标）
    with torch.no_grad():
        outputs_eval, load_balancing_loss_eval = model(inputs)
        accuracy = compute_accuracy(targets, outputs_eval)
        flops = estimate_flops(model, inputs.shape)

        # 奖励函数：准确率 - FLOPs惩罚 - 负载均衡惩罚
        task_reward = accuracy.item() if isinstance(accuracy, torch.Tensor) else accuracy
        flops_penalty = lambda_flops * flops / 1e6  # 归一化FLOPs
        balance_penalty = lambda_balance * load_balancing_loss_eval.item() if load_balancing_loss_eval is not None else 0

        total_reward = task_reward - flops_penalty - balance_penalty

    # 重新前向传播用于计算损失（需要梯度）
    outputs, load_balancing_loss = model(inputs)
    main_loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))

    # PPO更新（使用detached的奖励值）
    ppo_trainer.update(total_reward)

    return {
        'main_loss': main_loss.item(),
        'load_balancing_loss': load_balancing_loss.item() if load_balancing_loss is not None else 0,
        'accuracy': task_reward,
        'flops': flops / 1e6,
        'reward': total_reward
    }

# 主训练函数
def train_model():
    """完整的训练流程"""
    
    # 模型参数
    vocab_size = 30522
    embed_dim = 512  # 减小以节省内存
    num_heads = 8
    group_size = 2
    window_size = 1024  # 减小窗口大小
    num_experts = 32    # 减少专家数量
    top_k = 4
    num_layers = 4      # 减少层数
    seq_len = 64        # 减小序列长度
    
    print("初始化模型...")
    model = ImprovedDynamicMoETransformer(
        vocab_size, embed_dim, num_heads, group_size, window_size,
        num_experts, top_k, num_layers
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建数据
    print("创建训练数据...")
    batch_size = 8
    num_samples = 1000
    
    # 生成随机数据（实际应用中应使用真实数据）
    data = np.random.randint(1, vocab_size, (num_samples, seq_len))
    labels = np.roll(data, -1, axis=1)  # 简单的下一个token预测任务
    
    dataset = SimpleTextDataset(data, labels, seq_len)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    # 初始化训练组件
    criterion = nn.CrossEntropyLoss(ignore_index=0)  # 忽略padding token
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
    ppo_trainer = SimplePPOTrainer(model, lr=3e-4)
    monitor = PerformanceMonitor()
    
    # 训练循环
    num_epochs = 5
    print(f"开始训练 {num_epochs} 个epochs...")
    
    for epoch in range(num_epochs):
        model.train()
        epoch_stats = {'loss': [], 'accuracy': [], 'reward': []}
        
        for step, batch in enumerate(dataloader):
            optimizer.zero_grad()
            
            # 标准训练步骤
            inputs, targets = batch
            outputs, load_balancing_loss = model(inputs)
            
            # 主要损失
            main_loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
            
            # 添加负载均衡损失
            total_loss = main_loss
            if load_balancing_loss is not None:
                total_loss = total_loss + 0.01 * load_balancing_loss
            
            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 动态MoE训练步骤（确保梯度计算正确）
            with torch.set_grad_enabled(True):
                moe_stats = train_dynamic_moe_step(
                    model, ppo_trainer, batch, criterion,
                    lambda_flops=0.001, lambda_balance=0.01
                )
            
            # 记录统计信息
            epoch_stats['loss'].append(total_loss.item())
            epoch_stats['accuracy'].append(moe_stats['accuracy'])
            epoch_stats['reward'].append(moe_stats['reward'])
            
            # 更新监控器
            monitor.update(
                accuracy=moe_stats['accuracy'],
                loss=total_loss.item(),
                reward=moe_stats['reward'],
                flops_efficiency=1.0 / (moe_stats['flops'] + 1e-6)
            )
            
            # 打印进度
            if step % 20 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Step {step+1}/{len(dataloader)}, '
                      f'Loss: {total_loss.item():.4f}, '
                      f'Accuracy: {moe_stats["accuracy"]:.4f}, '
                      f'Reward: {moe_stats["reward"]:.4f}, '
                      f'FLOPs: {moe_stats["flops"]:.2f}M')
        
        # Epoch结束统计
        avg_loss = np.mean(epoch_stats['loss'])
        avg_accuracy = np.mean(epoch_stats['accuracy'])
        avg_reward = np.mean(epoch_stats['reward'])
        
        print(f'\nEpoch {epoch+1} 完成:')
        print(f'  平均损失: {avg_loss:.4f}')
        print(f'  平均准确率: {avg_accuracy:.4f}')
        print(f'  平均奖励: {avg_reward:.4f}')
        
        # 打印专家利用率统计
        print("\n专家利用率统计:")
        for layer_idx, layer in enumerate(model.layers):
            if hasattr(layer, 'moe'):
                expert_counts = layer.moe.expert_counts
                total_tokens = layer.moe.total_tokens
                if total_tokens > 0:
                    utilization = expert_counts / total_tokens
                    print(f'  Layer {layer_idx}: 最大利用率={utilization.max():.4f}, '
                          f'最小利用率={utilization.min():.4f}, '
                          f'标准差={utilization.std():.4f}')
        
        # 每2个epoch打印性能摘要
        if (epoch + 1) % 2 == 0:
            monitor.print_summary()
    
    print("\n训练完成！")
    return model, monitor

# 评估函数
def evaluate_model(model, dataloader):
    """评估模型性能"""
    model.eval()
    total_loss = 0
    total_accuracy = 0
    total_flops = 0
    num_batches = 0
    
    criterion = nn.CrossEntropyLoss(ignore_index=0)
    
    with torch.no_grad():
        for inputs, targets in dataloader:
            outputs, _ = model(inputs)
            loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
            accuracy = compute_accuracy(targets, outputs)
            flops = estimate_flops(model, inputs.shape)
            
            total_loss += loss.item()
            total_accuracy += accuracy.item() if isinstance(accuracy, torch.Tensor) else accuracy
            total_flops += flops
            num_batches += 1
    
    avg_loss = total_loss / num_batches
    avg_accuracy = total_accuracy / num_batches
    avg_flops = total_flops / num_batches
    
    print(f'\n=== 评估结果 ===')
    print(f'损失: {avg_loss:.4f}')
    print(f'准确率: {avg_accuracy:.4f}')
    print(f'平均FLOPs: {avg_flops/1e6:.2f}M')
    print('=' * 20)
    
    return {
        'loss': avg_loss,
        'accuracy': avg_accuracy,
        'flops': avg_flops
    }

if __name__ == "__main__":
    print("开始动态MoE训练...")
    start_time = time.time()
    
    # 训练模型
    trained_model, monitor = train_model()
    
    training_time = time.time() - start_time
    print(f"\n总训练时间: {training_time:.2f} 秒")
    
    # 最终性能摘要
    monitor.print_summary(last_n=100)
    
    print("\n训练完成！模型已保存。")
