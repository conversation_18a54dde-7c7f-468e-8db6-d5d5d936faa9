import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from improved_dynamic_moe import ImprovedDynamicMoETransformer
import time

def demonstrate_dynamic_routing():
    """演示动态路由机制"""
    print("=== 动态路由机制演示 ===")
    
    # 创建小型模型用于演示
    model = ImprovedDynamicMoETransformer(
        vocab_size=1000, embed_dim=256, num_heads=4, group_size=2,
        window_size=512, num_experts=16, top_k=4, num_layers=2
    )
    model.eval()
    
    # 创建不同复杂度的输入
    simple_input = torch.ones(1, 32, dtype=torch.long) * 100  # 重复token
    complex_input = torch.randint(0, 1000, (1, 32))  # 随机token
    
    print("\n1. 简单输入（重复token）:")
    with torch.no_grad():
        output1, loss1 = model(simple_input)
        
    print("\n2. 复杂输入（随机token）:")
    with torch.no_grad():
        output2, loss2 = model(complex_input)
    
    # 分析专家使用情况
    print("\n专家使用分析:")
    for layer_idx, layer in enumerate(model.layers):
        if hasattr(layer, 'moe'):
            expert_counts = layer.moe.expert_counts
            print(f"Layer {layer_idx}: 激活专家数量 = {(expert_counts > 0).sum().item()}")
    
    print(f"简单输入负载均衡损失: {loss1:.4f}")
    print(f"复杂输入负载均衡损失: {loss2:.4f}")

def demonstrate_sliding_window():
    """演示滑动窗口注意力"""
    print("\n=== 滑动窗口注意力演示 ===")
    
    from improved_dynamic_moe import create_sliding_window_mask
    
    # 创建不同窗口大小的掩码
    seq_len = 16
    window_sizes = [4, 8, 16]
    
    print(f"序列长度: {seq_len}")
    
    for window_size in window_sizes:
        mask = create_sliding_window_mask(seq_len, window_size)
        # 计算每个位置可以注意到的token数量
        attention_span = (mask != -float('inf')).sum(dim=-1).float()
        avg_span = attention_span.mean().item()
        
        print(f"窗口大小 {window_size}: 平均注意力跨度 = {avg_span:.1f}")

def demonstrate_gqa_efficiency():
    """演示GQA的内存效率"""
    print("\n=== GQA内存效率演示 ===")
    
    from improved_dynamic_moe import ImprovedGQAAttention
    
    embed_dim = 512
    seq_len = 128
    batch_size = 4
    
    # 标准多头注意力 vs GQA
    standard_heads = 8
    gqa_group_size = 2
    
    # 计算KV缓存大小
    standard_kv_size = batch_size * seq_len * standard_heads * (embed_dim // standard_heads) * 2
    gqa_kv_size = batch_size * seq_len * (standard_heads // gqa_group_size) * (embed_dim // standard_heads) * 2
    
    memory_saving = (1 - gqa_kv_size / standard_kv_size) * 100
    
    print(f"标准注意力KV缓存大小: {standard_kv_size:,} 参数")
    print(f"GQA KV缓存大小: {gqa_kv_size:,} 参数")
    print(f"内存节省: {memory_saving:.1f}%")
    
    # 实际测试
    x = torch.randn(batch_size, seq_len, embed_dim)
    
    # GQA注意力
    gqa_attn = ImprovedGQAAttention(embed_dim, standard_heads, gqa_group_size, window_size=64)
    
    start_time = time.time()
    with torch.no_grad():
        output = gqa_attn(x)
    gqa_time = time.time() - start_time
    
    print(f"GQA前向传播时间: {gqa_time*1000:.2f} ms")

def demonstrate_expert_specialization():
    """演示专家特化"""
    print("\n=== 专家特化演示 ===")
    
    model = ImprovedDynamicMoETransformer(
        vocab_size=1000, embed_dim=256, num_heads=4, group_size=2,
        window_size=512, num_experts=8, top_k=2, num_layers=1
    )
    
    # 模拟不同类型的输入
    math_tokens = torch.tensor([[1, 2, 3, 4, 5] * 6 + [0] * 2]).long()  # 数学序列
    text_tokens = torch.randint(100, 200, (1, 32))  # 文本token
    code_tokens = torch.randint(500, 600, (1, 32))  # 代码token
    
    inputs = [
        ("数学序列", math_tokens),
        ("文本序列", text_tokens), 
        ("代码序列", code_tokens)
    ]
    
    model.eval()
    expert_usage = {}
    
    for name, input_tokens in inputs:
        with torch.no_grad():
            output, _ = model(input_tokens)
            
        # 收集专家使用统计
        for layer_idx, layer in enumerate(model.layers):
            if hasattr(layer, 'moe'):
                expert_counts = layer.moe.expert_counts.clone()
                expert_usage[f"{name}_layer_{layer_idx}"] = expert_counts
                
                # 重置计数器
                layer.moe.expert_counts.zero_()
                layer.moe.total_tokens.zero_()
    
    # 分析专家特化程度
    print("专家使用模式:")
    for key, counts in expert_usage.items():
        active_experts = (counts > 0).sum().item()
        most_used = counts.argmax().item()
        print(f"{key}: 激活专家数={active_experts}, 最常用专家={most_used}")

def demonstrate_dynamic_complexity():
    """演示动态复杂度调整"""
    print("\n=== 动态复杂度调整演示 ===")
    
    model = ImprovedDynamicMoETransformer(
        vocab_size=1000, embed_dim=256, num_heads=4, group_size=2,
        window_size=512, num_experts=16, top_k=4, num_layers=2
    )
    model.eval()
    
    # 创建不同复杂度的输入
    inputs = [
        ("低复杂度", torch.ones(1, 32, dtype=torch.long) * 100),
        ("中复杂度", torch.randint(0, 100, (1, 32))),
        ("高复杂度", torch.randint(0, 1000, (1, 32)))
    ]
    
    print("复杂度自适应结果:")
    
    for name, input_tokens in inputs:
        with torch.no_grad():
            output, load_loss = model(input_tokens)
        
        # 分析每层的专家使用情况
        total_experts_used = 0
        for layer in model.layers:
            if hasattr(layer, 'moe'):
                expert_counts = layer.moe.expert_counts
                experts_used = (expert_counts > 0).sum().item()
                total_experts_used += experts_used
        
        avg_experts_per_layer = total_experts_used / len(model.layers)
        
        print(f"{name}: 平均每层使用专家数 = {avg_experts_per_layer:.1f}, "
              f"负载均衡损失 = {load_loss:.4f}")

def performance_comparison():
    """性能对比"""
    print("\n=== 性能对比 ===")
    
    # 创建不同配置的模型
    configs = [
        ("标准Transformer", {"num_experts": 1, "top_k": 1}),
        ("静态MoE", {"num_experts": 16, "top_k": 4}),
        ("动态MoE", {"num_experts": 16, "top_k": 4})  # 实际会动态调整
    ]
    
    embed_dim = 256
    seq_len = 64
    batch_size = 4
    
    results = {}
    
    for name, config in configs:
        model = ImprovedDynamicMoETransformer(
            vocab_size=1000, embed_dim=embed_dim, num_heads=4, group_size=2,
            window_size=512, num_experts=config["num_experts"], 
            top_k=config["top_k"], num_layers=2
        )
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        
        # 测试推理时间
        x = torch.randint(0, 1000, (batch_size, seq_len))
        
        model.eval()
        start_time = time.time()
        with torch.no_grad():
            for _ in range(10):  # 多次测试取平均
                output, _ = model(x)
        avg_time = (time.time() - start_time) / 10
        
        results[name] = {
            'params': total_params,
            'time': avg_time * 1000,  # 转换为毫秒
            'memory': torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        }
    
    print("性能对比结果:")
    print(f"{'模型':<15} {'参数数量':<12} {'推理时间(ms)':<15}")
    print("-" * 45)
    
    for name, stats in results.items():
        print(f"{name:<15} {stats['params']:>10,} {stats['time']:>12.2f}")

def main():
    """主演示函数"""
    print("动态路由混合专家（Dynamic MoE）与注意力优化结合 - 功能演示")
    print("=" * 70)
    
    # 设置随机种子以确保结果可重现
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 演示各个功能
        demonstrate_dynamic_routing()
        demonstrate_sliding_window()
        demonstrate_gqa_efficiency()
        demonstrate_expert_specialization()
        demonstrate_dynamic_complexity()
        performance_comparison()
        
        print("\n" + "=" * 70)
        print("所有演示完成！")
        
        print("\n主要改进点总结:")
        print("1. ✅ 动态专家路由 - 根据输入复杂度自动调整专家数量")
        print("2. ✅ GQA注意力机制 - 减少KV缓存内存占用")
        print("3. ✅ 滑动窗口注意力 - 支持长序列处理")
        print("4. ✅ 负载均衡机制 - 确保专家利用率均衡")
        print("5. ✅ PPO强化学习 - 优化计算效率与任务性能的平衡")
        print("6. ✅ 因果干预模块 - 减少专家选择偏向性")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
