import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import torch.nn.functional as F
import math
from typing import Optional, Tuple, List
import warnings

# 定义专家网络（FFN）
class FFN(nn.Module):
    def __init__(self, input_dim=768, hidden_dim=1024, dropout=0.1):
        super(FFN, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, input_dim)
        self.act = nn.GELU()
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.dropout(x)
        x = self.fc2(x)
        return x

# 定义动态MoE层
class DynamicMoE(nn.Module):
    def __init__(self, num_experts=256, top_k=4, hidden_dim=768, capacity_factor=1.25,
                 use_load_balancing=True, use_dynamic_k=True):
        super(DynamicMoE, self).__init__()
        self.num_experts = num_experts
        self.top_k = top_k
        self.hidden_dim = hidden_dim
        self.capacity_factor = capacity_factor
        self.use_load_balancing = use_load_balancing
        self.use_dynamic_k = use_dynamic_k

        # 专家网络
        self.experts = nn.ModuleList([FFN(hidden_dim) for _ in range(num_experts)])

        # 门控网络 - 增强版
        self.gating = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, num_experts)
        )

        # 动态top_k预测器
        if use_dynamic_k:
            self.k_predictor = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 4),
                nn.ReLU(),
                nn.Linear(hidden_dim // 4, 1),
                nn.Sigmoid()
            )

        # 负载均衡统计
        self.register_buffer('expert_counts', torch.zeros(num_experts))
        self.register_buffer('total_tokens', torch.tensor(0.0))

        # 因果干预模块
        self.causal_intervention = nn.Linear(hidden_dim, hidden_dim)

    def forward(self, x, attention_entropy=None):
        batch_size, seq_len, hidden_dim = x.shape

        # 计算注意力熵（如果没有提供）
        if attention_entropy is None:
            attention_entropy = self._compute_attention_entropy(x)

        # 动态调整top_k
        if self.use_dynamic_k:
            # 基于注意力熵动态调整专家数量
            k_ratio = self.k_predictor(x.mean(dim=1))  # [batch_size, 1]
            dynamic_k = torch.clamp(
                (k_ratio * self.num_experts).int(),
                min=2, max=min(self.top_k * 2, self.num_experts)
            )
        else:
            dynamic_k = torch.full((batch_size,), self.top_k, device=x.device)

        # 因果干预 - 减少偏向性
        x_intervened = x + 0.1 * self.causal_intervention(x)

        # 门控计算
        gate_logits = self.gating(x_intervened.mean(dim=1))  # [batch_size, num_experts]
        gate_probs = F.softmax(gate_logits, dim=-1)

        # 动态专家选择
        outputs = []
        load_balancing_loss = 0.0

        for i in range(batch_size):
            k = dynamic_k[i].item()
            weights, indices = torch.topk(gate_probs[i], k, dim=-1)

            # 重新归一化权重
            weights = weights / weights.sum()

            # 计算专家输出
            expert_outputs = []
            for j, expert_idx in enumerate(indices):
                expert_output = self.experts[expert_idx](x[i])  # [seq_len, hidden_dim]
                expert_outputs.append(weights[j] * expert_output)

            # 加权组合
            combined_output = torch.stack(expert_outputs).sum(dim=0)
            outputs.append(combined_output)

            # 更新负载统计
            if self.training:
                for expert_idx in indices:
                    self.expert_counts[expert_idx] += 1
                self.total_tokens += seq_len

        output = torch.stack(outputs, dim=0)

        # 计算负载均衡损失
        if self.use_load_balancing and self.training:
            load_balancing_loss = self._compute_load_balancing_loss(gate_probs)

        return output, load_balancing_loss

    def _compute_attention_entropy(self, x):
        """计算注意力熵作为复杂度指标"""
        # 简化的注意力熵计算
        attn_weights = F.softmax(torch.matmul(x, x.transpose(-2, -1)) / math.sqrt(x.size(-1)), dim=-1)
        entropy = -(attn_weights * torch.log(attn_weights + 1e-8)).sum(dim=-1).mean(dim=-1)
        return entropy

    def _compute_load_balancing_loss(self, gate_probs):
        """计算负载均衡损失"""
        # 计算每个专家的平均概率
        mean_probs = gate_probs.mean(dim=0)  # [num_experts]

        # 目标是均匀分布
        uniform_prob = 1.0 / self.num_experts

        # KL散度损失
        kl_loss = F.kl_div(
            torch.log(mean_probs + 1e-8),
            torch.full_like(mean_probs, uniform_prob),
            reduction='batchmean'
        )

        return kl_loss

    def get_expert_utilization(self):
        """获取专家利用率统计"""
        if self.total_tokens > 0:
            utilization = self.expert_counts / self.total_tokens
            return {
                'expert_counts': self.expert_counts.cpu().numpy(),
                'utilization': utilization.cpu().numpy(),
                'max_utilization': utilization.max().item(),
                'min_utilization': utilization.min().item(),
                'std_utilization': utilization.std().item()
            }
        return None

# 创建滑动窗口掩码
def create_sliding_window_mask(seq_len, window_size, device=None):
    """
    创建滑动窗口注意力掩码
    Args:
        seq_len: 序列长度
        window_size: 窗口大小
        device: 设备
    Returns:
        mask: 注意力掩码，形状为 [seq_len, seq_len]
    """
    if device is None:
        device = torch.device('cpu')

    # 创建因果掩码（下三角矩阵）
    causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=device))

    # 创建滑动窗口掩码
    window_mask = torch.zeros(seq_len, seq_len, device=device)
    for i in range(seq_len):
        start = max(0, i - window_size + 1)
        end = i + 1
        window_mask[i, start:end] = 1

    # 组合因果掩码和滑动窗口掩码
    mask = causal_mask * window_mask

    # 转换为注意力掩码格式（0表示可以注意，-inf表示不能注意）
    attention_mask = torch.where(mask == 0, torch.tensor(-float('inf'), device=device), torch.tensor(0.0, device=device))

    return attention_mask

# 定义GQA与滑动窗口注意力
class GQASlidingWindowAttention(nn.Module):
    def __init__(self, embed_dim, num_heads=8, group_size=2, window_size=4096, dropout=0.1,
                 use_dynamic_window=True):
        super(GQASlidingWindowAttention, self).__init__()
        assert num_heads % group_size == 0, "num_heads must be divisible by group_size"

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_kv_heads = num_heads // group_size  # 键值头数量
        self.group_size = group_size
        self.base_window_size = window_size
        self.head_dim = embed_dim // num_heads
        self.scaling = self.head_dim ** -0.5
        self.use_dynamic_window = use_dynamic_window

        # 投影矩阵
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, self.num_kv_heads * self.head_dim)
        self.v_proj = nn.Linear(embed_dim, self.num_kv_heads * self.head_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)

        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)

        # 动态窗口大小预测器
        if use_dynamic_window:
            self.window_predictor = nn.Sequential(
                nn.Linear(embed_dim, embed_dim // 4),
                nn.ReLU(),
                nn.Linear(embed_dim // 4, 1),
                nn.Sigmoid()
            )

        # 注意力熵计算
        self.attention_entropy = None

    def forward(self, x, attention_mask=None):
        """
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, embed_dim]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len, seq_len]
        """
        batch_size, seq_len, _ = x.shape

        # 动态调整窗口大小
        if self.use_dynamic_window:
            # 基于输入复杂度预测窗口大小
            window_ratio = self.window_predictor(x.mean(dim=1))  # [batch_size, 1]
            # 窗口大小在基础窗口的0.5倍到2倍之间
            window_size = torch.clamp(
                (window_ratio * self.base_window_size * 2).int(),
                min=self.base_window_size // 2,
                max=self.base_window_size * 2
            )
        else:
            window_size = torch.full((batch_size,), self.base_window_size, device=x.device)

        # 投影查询、键、值
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_kv_heads, self.head_dim)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_kv_heads, self.head_dim)

        # 重塑查询以适应GQA
        q = q.transpose(1, 2)  # [batch_size, num_heads, seq_len, head_dim]

        # 重复键值以匹配查询头数量
        k = torch.repeat_interleave(k, self.group_size, dim=2)  # [batch_size, seq_len, num_heads, head_dim]
        v = torch.repeat_interleave(v, self.group_size, dim=2)  # [batch_size, seq_len, num_heads, head_dim]

        # 转置键值
        k = k.transpose(1, 2)  # [batch_size, num_heads, seq_len, head_dim]
        v = v.transpose(1, 2)  # [batch_size, num_heads, seq_len, head_dim]

        # 计算注意力分数
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) * self.scaling  # [batch_size, num_heads, seq_len, seq_len]

        # 应用滑动窗口掩码
        attn_outputs = []
        attention_entropies = []

        for i in range(batch_size):
            # 为每个样本创建滑动窗口掩码
            window_mask = create_sliding_window_mask(seq_len, window_size[i].item(), device=x.device)

            # 应用掩码
            masked_attn = attn_weights[i] + window_mask

            # 如果提供了额外的注意力掩码，也应用它
            if attention_mask is not None:
                masked_attn = masked_attn + attention_mask[i]

            # Softmax
            attn_probs = F.softmax(masked_attn, dim=-1)

            # 计算注意力熵
            entropy = -(attn_probs * torch.log(attn_probs + 1e-8)).sum(dim=-1).mean()
            attention_entropies.append(entropy)

            # Dropout
            attn_probs = self.attn_dropout(attn_probs)

            # 应用注意力
            attn_output = torch.matmul(attn_probs, v[i])  # [num_heads, seq_len, head_dim]
            attn_outputs.append(attn_output)

        # 合并批次
        attn_output = torch.stack(attn_outputs)  # [batch_size, num_heads, seq_len, head_dim]
        self.attention_entropy = torch.stack(attention_entropies)  # [batch_size]

        # 重塑输出
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, self.embed_dim)

        # 输出投影
        attn_output = self.out_proj(attn_output)
        attn_output = self.resid_dropout(attn_output)

        return attn_output

    def get_attention_entropy(self):
        """获取注意力熵"""
        return self.attention_entropy

# 定义Transformer编码器层
class TransformerEncoderLayer(nn.Module):
    def __init__(self, embed_dim, num_heads, group_size, window_size, num_experts, top_k,
                 dropout=0.1, use_pre_norm=True):
        super(TransformerEncoderLayer, self).__init__()
        self.embed_dim = embed_dim
        self.use_pre_norm = use_pre_norm

        # 自注意力层
        self.self_attn = GQASlidingWindowAttention(
            embed_dim, num_heads, group_size, window_size, dropout
        )

        # 动态MoE层
        self.moe = DynamicMoE(
            num_experts, top_k, embed_dim,
            use_load_balancing=True, use_dynamic_k=True
        )

        # 层归一化
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

        # 用于存储中间状态
        self.attention_entropy = None
        self.load_balancing_loss = None

    def forward(self, x, attention_mask=None):
        """
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, embed_dim]
            attention_mask: 注意力掩码
        Returns:
            x: 输出张量
        """
        residual = x

        # Pre-norm或Post-norm
        if self.use_pre_norm:
            x = self.norm1(x)

        # 自注意力
        attn_output = self.self_attn(x, attention_mask)
        self.attention_entropy = self.self_attn.get_attention_entropy()

        # 残差连接
        x = residual + self.dropout(attn_output)

        if not self.use_pre_norm:
            x = self.norm1(x)

        # MoE层
        residual = x
        if self.use_pre_norm:
            x = self.norm2(x)

        # 使用注意力熵指导MoE
        moe_output, load_balancing_loss = self.moe(x, self.attention_entropy)
        self.load_balancing_loss = load_balancing_loss

        # 残差连接
        x = residual + self.dropout(moe_output)

        if not self.use_pre_norm:
            x = self.norm2(x)

        return x

    def get_auxiliary_losses(self):
        """获取辅助损失"""
        return {
            'load_balancing_loss': self.load_balancing_loss,
            'attention_entropy': self.attention_entropy.mean() if self.attention_entropy is not None else 0.0
        }

# 定义完整的Transformer模型
class DynamicMoETransformer(nn.Module):
    def __init__(self, vocab_size, embed_dim, num_heads, group_size, window_size, num_experts, top_k, num_layers):
        super(DynamicMoETransformer, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.encoder_layers = nn.ModuleList([
            TransformerEncoderLayer(embed_dim, num_heads, group_size, window_size, num_experts, top_k) 
            for _ in range(num_layers)
        ])
        self.fc_out = nn.Linear(embed_dim, vocab_size)

    def forward(self, x):
        x = self.embedding(x)
        for layer in self.encoder_layers:
            x = layer(x)
        x = self.fc_out(x)
        return x

# 示例参数
vocab_size = 30522  # 例如BERT的词汇表大小
embed_dim = 768
num_heads = 8
group_size = 2
window_size = 4096
num_experts = 256
top_k = 4
num_layers = 6

# 创建模型实例
model = DynamicMoETransformer(vocab_size, embed_dim, num_heads, group_size, window_size, num_experts, top_k, num_layers)

# 简单的数据集类
class SimpleDataset(Dataset):
    def __init__(self, data, labels):
        self.data = data
        self.labels = labels

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return self.data[idx], self.labels[idx]

# 假设我们有一些数据
data = np.random.randint(0, vocab_size, (1000, 128))  # 1000个样本，每个样本长度为128
labels = np.random.randint(0, vocab_size, (1000, 128))

dataset = SimpleDataset(data, labels)
dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# PPO训练器实现
class PPOTrainer:
    def __init__(self, model, lr=3e-4, eps_clip=0.2, value_coef=0.5, entropy_coef=0.01):
        self.model = model
        self.lr = lr
        self.eps_clip = eps_clip
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef

        # 为门控网络创建单独的优化器
        self.gate_params = []
        for layer in model.encoder_layers:
            if hasattr(layer, 'moe') and hasattr(layer.moe, 'gating'):
                self.gate_params.extend(layer.moe.gating.parameters())
                if hasattr(layer.moe, 'k_predictor'):
                    self.gate_params.extend(layer.moe.k_predictor.parameters())

        self.optimizer = optim.Adam(self.gate_params, lr=lr)

        # 存储训练历史
        self.rewards_history = []
        self.policy_losses = []

    def update(self, reward):
        """更新策略网络"""
        if isinstance(reward, (int, float)):
            reward = torch.tensor(reward, dtype=torch.float32)

        # 简化的策略梯度更新
        policy_loss = -reward  # 最大化奖励

        self.optimizer.zero_grad()
        policy_loss.backward(retain_graph=True)

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.gate_params, max_norm=1.0)

        self.optimizer.step()

        # 记录历史
        self.rewards_history.append(reward.item())
        self.policy_losses.append(policy_loss.item())

    def get_training_stats(self):
        """获取训练统计信息"""
        if len(self.rewards_history) > 0:
            return {
                'avg_reward': np.mean(self.rewards_history[-100:]),  # 最近100步的平均奖励
                'avg_policy_loss': np.mean(self.policy_losses[-100:]),
                'total_updates': len(self.rewards_history)
            }
        return {'avg_reward': 0, 'avg_policy_loss': 0, 'total_updates': 0}

# 初始化PPO训练器
ppo_trainer = PPOTrainer(model)

# KL散度
def kl_divergence(p, q):
    return (p * (p / q).log()).sum()

# 训练动态MoE
def train_dynamic_moe(model, ppo_trainer, batch, lambda_flops=0.01, lambda_balance=0.01):
    x, y = batch

    # 前向传播获取模型输出和辅助损失
    model_output = model(x)

    # 收集所有MoE层的负载均衡损失
    total_balance_loss = 0.0
    total_flops = 0.0

    for layer in model.encoder_layers:
        if hasattr(layer, 'moe'):
            # 获取专家利用率统计
            expert_stats = layer.moe.get_expert_utilization()
            if expert_stats is not None:
                # 计算FLOPs（基于专家利用率）
                layer_flops = expert_stats['utilization'].sum() * 1000000  # 假设每个专家100万FLOPs
                total_flops += layer_flops

            # 获取负载均衡损失
            aux_losses = layer.get_auxiliary_losses()
            if aux_losses['load_balancing_loss'] is not None:
                total_balance_loss += aux_losses['load_balancing_loss']

    # 计算任务奖励
    task_reward = compute_task_accuracy(y, model_output)

    # 计算总奖励：任务准确率 - 计算成本惩罚 - 负载均衡惩罚
    total_reward = task_reward - lambda_flops * total_flops / 1e6 - lambda_balance * total_balance_loss

    # 更新PPO训练器
    ppo_trainer.update(total_reward)

    return {
        'task_reward': task_reward,
        'flops_cost': total_flops / 1e6,
        'balance_loss': total_balance_loss.item() if isinstance(total_balance_loss, torch.Tensor) else total_balance_loss,
        'total_reward': total_reward.item() if isinstance(total_reward, torch.Tensor) else total_reward
    }

# 计算任务准确率
def compute_task_accuracy(y_true, y_pred):
    """
    计算任务准确率
    Args:
        y_true: 真实标签，形状为 [batch_size, seq_len]
        y_pred: 预测输出，形状为 [batch_size, seq_len, vocab_size]
    Returns:
        accuracy: 准确率
    """
    if y_pred.dim() == 3:
        # 如果是语言模型输出，取最大概率的token
        _, predicted = torch.max(y_pred, dim=-1)
    else:
        predicted = y_pred

    # 计算准确率（忽略padding token，假设为0）
    mask = (y_true != 0).float()  # 假设0是padding token
    correct = (predicted == y_true).float() * mask
    accuracy = correct.sum() / mask.sum() if mask.sum() > 0 else torch.tensor(0.0)

    return accuracy

# 计算FLOPs
def compute_flops(model, input_shape, num_experts_used=None):
    """
    估算模型FLOPs
    Args:
        model: 模型
        input_shape: 输入形状 (batch_size, seq_len)
        num_experts_used: 使用的专家数量（可选）
    Returns:
        total_flops: 总FLOPs
    """
    batch_size, seq_len = input_shape
    embed_dim = model.embedding.embedding_dim

    # 基础Transformer FLOPs
    # 注意力机制: 4 * batch_size * seq_len^2 * embed_dim
    attention_flops = 4 * batch_size * seq_len * seq_len * embed_dim

    # FFN FLOPs (如果没有MoE): 8 * batch_size * seq_len * embed_dim^2
    # 对于MoE，只计算激活的专家
    if num_experts_used is None:
        num_experts_used = 4  # 默认top-4

    moe_flops = 8 * batch_size * seq_len * embed_dim * embed_dim * num_experts_used / 256  # 假设256个专家

    # 总FLOPs
    total_flops = (attention_flops + moe_flops) * len(model.encoder_layers)

    return total_flops

# 性能评估工具
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'accuracy': [],
            'flops': [],
            'expert_utilization': [],
            'attention_entropy': [],
            'load_balance_loss': []
        }

    def update(self, accuracy, flops, expert_stats=None, attention_entropy=None, balance_loss=None):
        self.metrics['accuracy'].append(accuracy)
        self.metrics['flops'].append(flops)

        if expert_stats:
            self.metrics['expert_utilization'].append(expert_stats['std_utilization'])

        if attention_entropy is not None:
            self.metrics['attention_entropy'].append(attention_entropy)

        if balance_loss is not None:
            self.metrics['load_balance_loss'].append(balance_loss)

    def get_summary(self, last_n=100):
        """获取最近n步的性能摘要"""
        summary = {}
        for key, values in self.metrics.items():
            if values:
                recent_values = values[-last_n:]
                summary[f'{key}_mean'] = np.mean(recent_values)
                summary[f'{key}_std'] = np.std(recent_values)
        return summary

    def print_summary(self, last_n=100):
        """打印性能摘要"""
        summary = self.get_summary(last_n)
        print(f"\n=== 性能摘要 (最近{last_n}步) ===")
        for key, value in summary.items():
            print(f"{key}: {value:.4f}")
        print("=" * 40)

# 改进的训练循环
def train_model_with_dynamic_moe():
    """完整的动态MoE训练流程"""

    # 初始化性能监控器
    monitor = PerformanceMonitor()

    # 训练循环
    num_epochs = 10
    print("开始训练动态MoE模型...")

    for epoch in range(num_epochs):
        model.train()
        epoch_loss = 0
        epoch_rewards = []

        for i, (inputs, targets) in enumerate(dataloader):
            optimizer.zero_grad()

            # 前向传播
            outputs = model(inputs)

            # 计算主要损失
            main_loss = criterion(outputs.view(-1, vocab_size), targets.view(-1))

            # 收集辅助损失
            aux_loss = 0.0
            for layer in model.encoder_layers:
                if hasattr(layer, 'get_auxiliary_losses'):
                    aux_losses = layer.get_auxiliary_losses()
                    if aux_losses['load_balancing_loss'] is not None:
                        aux_loss += aux_losses['load_balancing_loss']

            # 总损失
            total_loss = main_loss + 0.01 * aux_loss

            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            # PPO更新门控网络
            moe_stats = train_dynamic_moe(
                model, ppo_trainer, (inputs, targets),
                lambda_flops=0.01, lambda_balance=0.01
            )

            # 计算性能指标
            accuracy = compute_task_accuracy(targets, outputs)
            flops = compute_flops(model, inputs.shape)

            # 更新监控器
            monitor.update(
                accuracy=accuracy.item() if isinstance(accuracy, torch.Tensor) else accuracy,
                flops=flops,
                balance_loss=aux_loss.item() if isinstance(aux_loss, torch.Tensor) else aux_loss
            )

            epoch_loss += total_loss.item()
            epoch_rewards.append(moe_stats['total_reward'])

            if i % 10 == 0:
                print(f'Epoch [{epoch+1}/{num_epochs}], Step [{i+1}/{len(dataloader)}], '
                      f'Loss: {total_loss.item():.4f}, Accuracy: {accuracy:.4f}, '
                      f'Reward: {moe_stats["total_reward"]:.4f}')

        # Epoch结束统计
        avg_epoch_loss = epoch_loss / len(dataloader)
        avg_epoch_reward = np.mean(epoch_rewards)

        print(f'Epoch {epoch+1} 完成 - 平均损失: {avg_epoch_loss:.4f}, 平均奖励: {avg_epoch_reward:.4f}')

        # 打印专家利用率统计
        for layer_idx, layer in enumerate(model.encoder_layers):
            if hasattr(layer, 'moe'):
                expert_stats = layer.moe.get_expert_utilization()
                if expert_stats:
                    print(f'Layer {layer_idx} 专家利用率 - 最大: {expert_stats["max_utilization"]:.4f}, '
                          f'最小: {expert_stats["min_utilization"]:.4f}, '
                          f'标准差: {expert_stats["std_utilization"]:.4f}')

        # 每个epoch后进行评估
        evaluate_model(model, dataloader, monitor)

        # 打印性能摘要
        if (epoch + 1) % 2 == 0:
            monitor.print_summary()

# 改进的评估函数
def evaluate_model(model, dataloader, monitor=None):
    """评估模型性能"""
    model.eval()
    total_loss = 0
    total_accuracy = 0
    total_flops = 0
    num_batches = 0

    with torch.no_grad():
        for inputs, targets in dataloader:
            outputs = model(inputs)
            loss = criterion(outputs.view(-1, vocab_size), targets.view(-1))

            # 计算准确率
            accuracy = compute_task_accuracy(targets, outputs)

            # 计算FLOPs
            flops = compute_flops(model, inputs.shape)

            total_loss += loss.item()
            total_accuracy += accuracy.item() if isinstance(accuracy, torch.Tensor) else accuracy
            total_flops += flops
            num_batches += 1

    avg_loss = total_loss / num_batches
    avg_accuracy = total_accuracy / num_batches
    avg_flops = total_flops / num_batches

    print(f'验证结果 - 损失: {avg_loss:.4f}, 准确率: {avg_accuracy:.4f}, '
          f'平均FLOPs: {avg_flops/1e6:.2f}M')

    if monitor:
        monitor.update(accuracy=avg_accuracy, flops=avg_flops)

    return {
        'loss': avg_loss,
        'accuracy': avg_accuracy,
        'flops': avg_flops
    }

# 执行训练
train_model_with_dynamic_moe()

# 量化-蒸馏部署阶段
class ModelQuantizer:
    """简化的4-bit量化实现"""
    @staticmethod
    def quantize_model_4bit(model):
        """4-bit量化模型（简化实现）"""
        quantized_model = type(model)(
            model.embedding.num_embeddings,
            model.embedding.embedding_dim,
            8,  # num_heads
            2,  # group_size
            4096,  # window_size
            256,  # num_experts
            4,  # top_k
            len(model.encoder_layers)  # num_layers
        )

        # 复制权重（实际应用中需要真正的量化）
        quantized_model.load_state_dict(model.state_dict())

        # 模拟量化效果：添加噪声
        with torch.no_grad():
            for param in quantized_model.parameters():
                if param.requires_grad:
                    noise = torch.randn_like(param) * 0.01
                    param.add_(noise)

        return quantized_model

class DistillationTrainer:
    """知识蒸馏训练器"""
    def __init__(self, teacher, student, loss_fn, optimizer, temperature=4.0, alpha=0.7):
        self.teacher = teacher
        self.student = student
        self.loss_fn = loss_fn
        self.optimizer = optimizer
        self.temperature = temperature
        self.alpha = alpha

        # 冻结教师模型
        for param in self.teacher.parameters():
            param.requires_grad = False
        self.teacher.eval()

    def distillation_loss(self, student_logits, teacher_logits, targets):
        """计算蒸馏损失"""
        # 软目标损失
        soft_targets = F.softmax(teacher_logits / self.temperature, dim=-1)
        soft_student = F.log_softmax(student_logits / self.temperature, dim=-1)
        soft_loss = F.kl_div(soft_student, soft_targets, reduction='batchmean')
        soft_loss = soft_loss * (self.temperature ** 2)

        # 硬目标损失
        hard_loss = self.loss_fn(student_logits, targets)

        # 组合损失
        total_loss = self.alpha * soft_loss + (1 - self.alpha) * hard_loss
        return total_loss, soft_loss, hard_loss

    def train_epoch(self, dataloader):
        """训练一个epoch"""
        self.student.train()
        total_loss = 0
        total_soft_loss = 0
        total_hard_loss = 0

        for batch_idx, (inputs, targets) in enumerate(dataloader):
            self.optimizer.zero_grad()

            # 教师模型预测
            with torch.no_grad():
                teacher_outputs = self.teacher(inputs)

            # 学生模型预测
            student_outputs = self.student(inputs)

            # 计算蒸馏损失
            loss, soft_loss, hard_loss = self.distillation_loss(
                student_outputs.view(-1, student_outputs.size(-1)),
                teacher_outputs.view(-1, teacher_outputs.size(-1)),
                targets.view(-1)
            )

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.student.parameters(), max_norm=1.0)
            self.optimizer.step()

            total_loss += loss.item()
            total_soft_loss += soft_loss.item()
            total_hard_loss += hard_loss.item()

            if batch_idx % 10 == 0:
                print(f'Batch {batch_idx}, Loss: {loss.item():.4f}, '
                      f'Soft: {soft_loss.item():.4f}, Hard: {hard_loss.item():.4f}')

        return {
            'total_loss': total_loss / len(dataloader),
            'soft_loss': total_soft_loss / len(dataloader),
            'hard_loss': total_hard_loss / len(dataloader)
        }

# 量化模型
print("开始模型量化...")
quantizer = ModelQuantizer()
quantized_model = quantizer.quantize_model_4bit(model)

# 知识蒸馏
print("开始知识蒸馏...")
distillation_optimizer = optim.Adam(quantized_model.parameters(), lr=1e-4)
distillation_trainer = DistillationTrainer(
    teacher=model,
    student=quantized_model,
    loss_fn=criterion,
    optimizer=distillation_optimizer
)

# 训练蒸馏模型
num_distill_epochs = 5
for epoch in range(num_distill_epochs):
    print(f"蒸馏训练 Epoch {epoch+1}/{num_distill_epochs}")
    stats = distillation_trainer.train_epoch(dataloader)
    print(f"蒸馏损失统计: {stats}")
    evaluate_model(quantized_model, dataloader)